#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import subprocess
import importlib

def check_python():
    """فحص إصدار Python"""
    print("=" * 50)
    print("فحص Python")
    print("=" * 50)
    
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print(f"نظام التشغيل: {sys.platform}")
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 8):
        print("❌ تحذير: يُنصح باستخدام Python 3.8 أو أحدث")
    else:
        print("✅ إصدار Python مناسب")
    
    print()

def check_pip():
    """فحص pip"""
    print("=" * 50)
    print("فحص pip")
    print("=" * 50)
    
    try:
        import pip
        print(f"✅ pip متوفر - الإصدار: {pip.__version__}")
    except ImportError:
        print("❌ pip غير متوفر")
        return False
    
    # فحص إصدار pip
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        print(f"إصدار pip: {result.stdout.strip()}")
    except Exception as e:
        print(f"خطأ في فحص pip: {e}")
    
    print()
    return True

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("=" * 50)
    print("فحص المكتبات المطلوبة")
    print("=" * 50)
    
    required_modules = [
        ("tkinter", "مكتبة واجهة المستخدم الأساسية"),
        ("pyodbc", "للاتصال بقاعدة البيانات"),
        ("customtkinter", "واجهة مستخدم محسنة"),
        ("PIL", "معالجة الصور"),
        ("ttkbootstrap", "تصميم واجهة المستخدم"),
        ("reportlab", "إنشاء ملفات PDF"),
        ("arabic_reshaper", "دعم النصوص العربية"),
        ("bidi", "دعم الكتابة من اليمين لليسار"),
        ("bcrypt", "تشفير كلمات المرور"),
        ("win32com.client", "للتعامل مع ملفات Access")
    ]
    
    missing_modules = []
    
    for module_name, description in required_modules:
        try:
            if module_name == "PIL":
                importlib.import_module("PIL")
            else:
                importlib.import_module(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError:
            print(f"❌ {module_name} - {description} (مفقود)")
            missing_modules.append(module_name)
    
    print()
    
    if missing_modules:
        print("المكتبات المفقودة:")
        for module in missing_modules:
            print(f"  - {module}")
        print()
        print("لتثبيت المكتبات المفقودة، استخدم الأمر:")
        print(f"pip install {' '.join(missing_modules)}")
        print()
        return False
    else:
        print("✅ جميع المكتبات المطلوبة متوفرة")
        return True

def check_files():
    """فحص الملفات المطلوبة"""
    print("=" * 50)
    print("فحص الملفات المطلوبة")
    print("=" * 50)
    
    required_files = [
        "main.py",
        "login.py",
        "main_app.py",
        "database.py",
        "config.py",
        "ui_components.py",
        "create_database.py",
        "raw_materials.py",
        "finished_products.py",
        "mixtures.py",
        "reports.py",
        "users.py",
        "upgrade_database.py",
        "requirements.txt"
    ]
    
    missing_files = []
    
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} (مفقود)")
            missing_files.append(file_name)
    
    print()
    
    if missing_files:
        print("الملفات المفقودة:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print("✅ جميع الملفات المطلوبة موجودة")
        return True

def main():
    print("تشخيص مشاكل برنامج مركز المخزون")
    print("=" * 50)
    print()
    
    # فحص Python
    check_python()
    
    # فحص pip
    pip_ok = check_pip()
    
    # فحص الملفات
    files_ok = check_files()
    
    # فحص المكتبات
    modules_ok = check_required_modules()
    
    # النتيجة النهائية
    print("=" * 50)
    print("النتيجة النهائية")
    print("=" * 50)
    
    if pip_ok and files_ok and modules_ok:
        print("✅ جميع المتطلبات متوفرة - يمكن تشغيل البرنامج")
        print()
        print("لتشغيل البرنامج، استخدم الأمر:")
        print("python main.py")
    else:
        print("❌ هناك مشاكل تحتاج إلى حل قبل تشغيل البرنامج")
        print()
        if not pip_ok:
            print("- قم بتثبيت pip")
        if not files_ok:
            print("- تأكد من وجود جميع ملفات البرنامج")
        if not modules_ok:
            print("- قم بتثبيت المكتبات المفقودة")
    
    print()
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
