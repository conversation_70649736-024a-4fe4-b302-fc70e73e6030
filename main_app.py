
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import sys
import time
from datetime import datetime
import pathlib

# استيراد مكتبات PDF بشكل آمن
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    print("تحذير: مكتبات دعم العربية غير متاحة")
    ARABIC_SUPPORT = False

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    PDF_SUPPORT = True
except ImportError:
    print("تحذير: مكتبات PDF غير متاحة")
    PDF_SUPPORT = False

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, UserManager, RawMaterialsManager, FinishedProductsManager, MixturesManager

# تسجيل الخطوط لدعم اللغة العربية في ملفات PDF
if PDF_SUPPORT:
    try:
        font_path = os.path.join(current_dir, "assets", "fonts")
        if not os.path.exists(font_path):
            os.makedirs(font_path)

        # يمكنك إضافة خط عربي هنا مثل Tajawal أو Amiri
        # pdfmetrics.registerFont(TTFont('Arabic', os.path.join(font_path, 'arabic_font.ttf')))
    except Exception as e:
        print(f"خطأ في تسجيل الخطوط: {e}")

class MainApplication(ttb.Window):
    def __init__(self, user=None):
        super().__init__(themename="pulse")

        # إعدادات النافذة
        self.title("مركز المخزون")
        self.geometry("1200x700")
        self.minsize(800, 600)

        # حفظ بيانات المستخدم الحالي
        self.current_user = user

        # إنشاء مديري قاعدة البيانات
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager(self.db_manager)
        self.raw_materials_manager = RawMaterialsManager(self.db_manager)
        self.products_manager = FinishedProductsManager(self.db_manager)
        self.mixtures_manager = MixturesManager(self.db_manager)

        # التأكد من الاتصال بقاعدة البيانات
        if not self.db_manager.connection:
            self.db_manager.connect()

        # تحميل الإعدادات
        self.settings = config.load_settings()

        # إنشاء واجهة المستخدم
        self.create_widgets()

        # تهيئة الشاشة الرئيسية عند البدء
        self.show_dashboard()

    def create_widgets(self):
        # إطار رئيسي
        self.main_frame = ttb.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء شريط القوائم
        self.create_menu()

        # إطار الشريط الجانبي
        sidebar_frame = ttb.Frame(self.main_frame, bootstyle="light")
        sidebar_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=0, pady=0)

        # شريط جانبي (للتنقل بين الشاشات)
        self.create_sidebar(sidebar_frame)

        # إطار المحتوى الرئيسي
        self.content_frame = ttb.Frame(self.main_frame)
        self.content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

    def create_menu(self):
        # إنشاء شريط القوائم
        menubar = tk.Menu(self)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label=ui.format_arabic_text("إعدادات النظام"), command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label=ui.format_arabic_text("تسجيل الخروج"), command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label=ui.format_arabic_text("خروج"), command=self.quit)
        menubar.add_cascade(label=ui.format_arabic_text("ملف"), menu=file_menu)

        # قائمة المواد الخام
        materials_menu = tk.Menu(menubar, tearoff=0)
        materials_menu.add_command(label=ui.format_arabic_text("إدارة المواد الخام"), command=self.show_raw_materials)
        menubar.add_cascade(label=ui.format_arabic_text("المواد الخام"), menu=materials_menu)

        # قائمة المنتجات
        products_menu = tk.Menu(menubar, tearoff=0)
        products_menu.add_command(label=ui.format_arabic_text("إدارة المنتجات الجاهزة"), command=self.show_products)
        menubar.add_cascade(label=ui.format_arabic_text("المنتجات الجاهزة"), menu=products_menu)

        # قائمة الخلطات
        mixtures_menu = tk.Menu(menubar, tearoff=0)
        mixtures_menu.add_command(label=ui.format_arabic_text("إدارة الخلطات"), command=self.show_mixtures)
        menubar.add_cascade(label=ui.format_arabic_text("الخلطات والمطاحين"), menu=mixtures_menu)

        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        reports_menu.add_command(label=ui.format_arabic_text("تقارير المواد الخام"), command=lambda: self.show_reports("raw_materials"))
        reports_menu.add_command(label=ui.format_arabic_text("تقارير المنتجات الجاهزة"), command=lambda: self.show_reports("products"))
        reports_menu.add_command(label=ui.format_arabic_text("تقارير الخلطات"), command=lambda: self.show_reports("mixtures"))
        menubar.add_cascade(label=ui.format_arabic_text("التقارير"), menu=reports_menu)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label=ui.format_arabic_text("دليل المستخدم"), command=self.show_help)
        help_menu.add_separator()
        help_menu.add_command(label=ui.format_arabic_text("حول البرنامج"), command=self.show_about)
        menubar.add_cascade(label=ui.format_arabic_text("مساعدة"), menu=help_menu)

        # وضع شريط القوائم في النافذة
        self.config(menu=menubar)

    def create_sidebar(self, parent):
        # إطار الشريط الجانبي
        sidebar_content = ttb.Frame(parent, bootstyle="light")
        sidebar_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شعار وعنوان التطبيق
        title_frame = ttb.Frame(sidebar_content, bootstyle="light")
        title_frame.pack(fill=tk.X, pady=(0, 20))

        app_title = ui.RTLLabel(
            title_frame, 
            text="نظام إدارة المخزون", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        app_title.pack(pady=5)

        # معلومات المستخدم
        if self.current_user:
            user_frame = ttb.Frame(sidebar_content, bootstyle="light")
            user_frame.pack(fill=tk.X, pady=5)

            user_label = ui.RTLLabel(
                user_frame, 
                text=f"مرحباً، {self.current_user.FullName}" if hasattr(self.current_user, 'FullName') else "مرحباً، المستخدم",
                bootstyle="secondary"
            )
            user_label.pack(pady=5)

        # أزرار التنقل
        buttons_frame = ttb.Frame(sidebar_content)
        buttons_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # أزرار القائمة
        menu_buttons = [
            ("الصفحة الرئيسية", self.show_dashboard),
            ("المواد الخام", self.show_raw_materials),
            ("المنتجات الجاهزة", self.show_products),
            ("الخلطات والمطاحين", self.show_mixtures),
            ("التقارير", lambda: self.show_reports("main")),
            ("المستخدمين", self.show_users)
        ]

        for text, command in menu_buttons:
            btn = ttb.Button(
                buttons_frame, 
                text=ui.format_arabic_text(text),
                command=command,
                bootstyle="link",
                width=25
            )
            btn.pack(fill=tk.X, pady=5, padx=5)

        # زر تسجيل الخروج
        separator = ttb.Separator(sidebar_content)
        separator.pack(fill=tk.X, pady=10)

        logout_btn = ttb.Button(
            sidebar_content, 
            text=ui.format_arabic_text("تسجيل الخروج"),
            command=self.logout,
            bootstyle="danger-outline",
            width=20
        )
        logout_btn.pack(pady=10)

    def clear_content_frame(self):
        # مسح جميع المكونات من إطار المحتوى
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        self.clear_content_frame()

        # إطار الترحيب والملخص
        welcome_frame = ttb.Frame(self.content_frame)
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان
        header_label = ui.RTLLabel(
            welcome_frame, 
            text="لوحة التحكم الرئيسية", 
            font=("Tajawal", 24, "bold"),
            bootstyle="primary"
        )
        header_label.pack(pady=(0, 20))

        # إحصائيات سريعة
        stats_frame = ttb.Frame(welcome_frame)
        stats_frame.pack(fill=tk.X, pady=10)

        # إنشاء بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "المواد الخام", "عدد المواد المسجلة", "materials_count")
        self.create_stat_card(stats_frame, "المنتجات الجاهزة", "عدد المنتجات المسجلة", "products_count")
        self.create_stat_card(stats_frame, "الخلطات", "عدد الخلطات المسجلة", "mixtures_count")

    def create_stat_card(self, parent, title, subtitle, data_type):
        # إطار البطاقة
        card = ttb.Frame(parent, bootstyle="light")
        card.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # عنوان البطاقة
        card_title = ui.RTLLabel(
            card, 
            text=title, 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        card_title.pack(pady=(10, 5), padx=10, anchor="e")

        # القيمة (يمكن استبدالها بقيمة فعلية من قاعدة البيانات)
        value = "0"  # قيمة افتراضية
        try:
            if data_type == "materials_count":
                materials = self.raw_materials_manager.get_all_materials()
                value = str(len(materials)) if materials else "0"
            elif data_type == "products_count":
                products = self.products_manager.get_all_products()
                value = str(len(products)) if products else "0"
            elif data_type == "mixtures_count":
                mixtures = self.mixtures_manager.get_all_mixtures()
                value = str(len(mixtures)) if mixtures else "0"
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            value = "؟"

        value_label = ui.RTLLabel(
            card, 
            text=value, 
            font=("Tajawal", 24, "bold"),
            bootstyle="secondary"
        )
        value_label.pack(pady=5, padx=10, anchor="e")

        # الوصف
        subtitle_label = ui.RTLLabel(
            card, 
            text=subtitle, 
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 10), padx=10, anchor="e")

    def show_raw_materials(self):
        # تحميل شاشة المواد الخام
        from raw_materials import RawMaterialsScreen
        self.clear_content_frame()

        materials_screen = RawMaterialsScreen(
            self.content_frame, 
            self.db_manager, 
            self.raw_materials_manager
        )
        materials_screen.pack(fill=tk.BOTH, expand=True)

    def show_products(self):
        # تحميل شاشة المنتجات الجاهزة
        from finished_products import FinishedProductsScreen
        self.clear_content_frame()

        products_screen = FinishedProductsScreen(
            self.content_frame, 
            self.db_manager, 
            self.products_manager
        )
        products_screen.pack(fill=tk.BOTH, expand=True)

    def show_mixtures(self):
        # تحميل شاشة الخلطات
        from mixtures import MixturesScreen
        self.clear_content_frame()

        mixtures_screen = MixturesScreen(
            self.content_frame, 
            self.db_manager, 
            self.mixtures_manager,
            self.raw_materials_manager,
            self.products_manager
        )
        mixtures_screen.pack(fill=tk.BOTH, expand=True)

    def show_reports(self, report_type):
        # تحميل شاشة التقارير
        from reports import ReportsScreen
        self.clear_content_frame()

        reports_screen = ReportsScreen(
            self.content_frame, 
            self.db_manager, 
            self.raw_materials_manager,
            self.products_manager,
            self.mixtures_manager,
            report_type
        )
        reports_screen.pack(fill=tk.BOTH, expand=True)

    def show_users(self):
        # تحميل شاشة المستخدمين
        from users import UsersScreen
        self.clear_content_frame()

        users_screen = UsersScreen(
            self.content_frame, 
            self.db_manager, 
            self.user_manager
        )
        users_screen.pack(fill=tk.BOTH, expand=True)

    def show_settings(self):
        # تحميل شاشة الإعدادات
        ui.arabic_messagebox(
            "إعدادات النظام", 
            "ستتوفر إعدادات النظام في الإصدار القادم.",
            "info"
        )

    def show_help(self):
        # عرض دليل المساعدة
        ui.arabic_messagebox(
            "دليل المستخدم", 
            "سيتوفر دليل المستخدم في الإصدار القادم.",
            "info"
        )

    def show_about(self):
        # عرض معلومات حول البرنامج
        ui.arabic_messagebox(
            "حول البرنامج", 
            "نظام إدارة مخزون المطحنة - الإصدار 1.0\n\nتم تطويره لإدارة المواد الخام والمنتجات والخلطات في المطاحن.",
            "info"
        )

    def logout(self):
        # تسجيل الخروج وإعادة فتح شاشة تسجيل الدخول
        if ui.arabic_messagebox(
            "تسجيل الخروج", 
            "هل ترغب بتسجيل الخروج من النظام؟",
            "yesno"
        ):
            # إغلاق الاتصال بقاعدة البيانات
            if self.db_manager.connection:
                self.db_manager.disconnect()

            # إغلاق النافذة الحالية
            self.destroy()

            # فتح شاشة تسجيل الدخول
            from login import LoginScreen
            login = LoginScreen()
            login.mainloop()

if __name__ == "__main__":
    # إذا تم تشغيل الملف مباشرة، افتح شاشة تسجيل الدخول
    from login import LoginScreen
    login = LoginScreen()
    login.mainloop()
