import sqlite3
import os

def create_complete_database():
    print("جاري إنشاء قاعدة البيانات...")
    
    # حذف قاعدة البيانات القديمة إن وجدت
    if os.path.exists("flour_mill_inventory.db"):
        os.remove("flour_mill_inventory.db")
    
    # إنشاء قاعدة بيانات جديدة
    conn = sqlite3.connect("flour_mill_inventory.db")
    cursor = conn.cursor()

    # إنشاء جدول المستخدمين
    cursor.execute("""
    CREATE TABLE Users (
        UserID INTEGER PRIMARY KEY AUTOINCREMENT,
        Username TEXT NOT NULL UNIQUE,
        Password TEXT NOT NULL,
        FullName TEXT NOT NULL,
        UserType TEXT NOT NULL,
        LastLogin DATETIME,
        IsActive INTEGER DEFAULT 1
    )
    """)

    # إضافة مستخدمين
    cursor.execute("""
    INSERT INTO Users (Username, Password, FullName, UserType)
    VALUES ('test', '123', 'مستخدم تجريبي', 'User')
    """)
    
    cursor.execute("""
    INSERT INTO Users (Username, Password, FullName, UserType)
    VALUES ('admin', 'admin', 'مدير النظام', 'Admin')
    """)

    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات بنجاح!")

if __name__ == "__main__":
    create_complete_database()