
import os
import json
from pathlib import Path

# المسارات الأساسية
BASE_DIR = Path(__file__).parent.absolute()
DB_PATH = os.path.join(BASE_DIR, "flour_mill_inventory.db")  # SQLite بدلاً من .accdb
CONFIG_PATH = os.path.join(BASE_DIR, 'config.json')
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
ASSETS_DIR = os.path.join(BASE_DIR, 'assets')

# إنشاء مجلدات إذا لم تكن موجودة
for directory in [REPORTS_DIR, ASSETS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# إعدادات الثيم والألوان
THEME_SETTINGS = {
    "primary_color": "#025464",    # لون أزرق داكن
    "secondary_color": "#E8AA42",  # لون برتقالي ذهبي
    "accent_color": "#E57C23",     # لون برتقالي داكن
    "bg_color": "#F8F1F1",         # لون خلفية فاتح
    "text_color": "#025464",       # لون النص الرئيسي
    "error_color": "#FF6464",      # لون الخطأ
    "success_color": "#5FD068"     # لون النجاح
}

# حفظ الإعدادات الافتراضية إذا لم تكن موجودة
def save_default_settings():
    if not os.path.exists(CONFIG_PATH):
        config = {
            "theme": THEME_SETTINGS,
            "company_name": "مركز المخزون",
            "company_logo": "assets/logo.txt",
            "language": "ar",
            "rtl": True,
            "db_path": DB_PATH
        }

        with open(CONFIG_PATH, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

# تحميل الإعدادات
def load_settings():
    if os.path.exists(CONFIG_PATH):
        with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        save_default_settings()
        return load_settings()

# الحصول على سلسلة اتصال قاعدة البيانات
def get_db_connection_string():
    # استخدام SQLite بدلاً من Access
    return f'sqlite:///{DB_PATH}'

if __name__ == "__main__":
    save_default_settings()
    print("تم إنشاء ملف الإعدادات بنجاح!")
