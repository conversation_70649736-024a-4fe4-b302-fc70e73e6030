#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from database import DatabaseManager

def create_simple_user():
    """إنشاء مستخدم بسيط بكلمة مرور غير مشفرة للاختبار"""
    print("=" * 50)
    print("إنشاء مستخدم بسيط للاختبار...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # الاتصال بقاعدة البيانات
        if not db.connect():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        # حذف المستخدم إذا كان موجوداً
        try:
            delete_query = "DELETE FROM Users WHERE Username = ?"
            db.execute_query(delete_query, ('test',))
            print("✅ تم حذف المستخدم السابق إن وجد")
        except:
            pass
        
        # إنشاء مستخدم جديد بكلمة مرور بسيطة
        query = """
        INSERT INTO Users (Username, Password, FullName, UserType)
        VALUES (?, ?, ?, ?)
        """
        
        result = db.execute_query(query, (
            'test',           # اسم المستخدم
            '123',            # كلمة المرور (غير مشفرة)
            'مستخدم تجريبي',   # الاسم الكامل
            'User'            # نوع المستخدم
        ))
        
        if result is not None:
            print("✅ تم إنشاء المستخدم التجريبي بنجاح!")
            print("اسم المستخدم: test")
            print("كلمة المرور: 123")
            print("\nيمكنك الآن تسجيل الدخول باستخدام هذه البيانات")
        else:
            print("❌ فشل في إنشاء المستخدم التجريبي")
        
        db.disconnect()
        return result is not None
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم التجريبي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إنشاء مستخدم تجريبي")
    create_simple_user()

if __name__ == "__main__":
    main()
