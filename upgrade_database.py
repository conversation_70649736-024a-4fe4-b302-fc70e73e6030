#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت ترقية قاعدة البيانات
يقوم بتشفير كلمات المرور الموجودة وإضافة تحسينات أمنية
"""

import os
import sys
import logging
from datetime import datetime
import shutil

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from database import DatabaseManager, SecurityManager, DatabaseError
    import config
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت جميع المكتبات المطلوبة")
    sys.exit(1)

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upgrade.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseUpgrader:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.security = SecurityManager()
        
    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            db_path = config.DB_PATH
            if not os.path.exists(db_path):
                logger.warning("قاعدة البيانات غير موجودة، سيتم إنشاؤها")
                return True
                
            backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(db_path, backup_path)
            logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def check_password_encryption(self):
        """فحص ما إذا كانت كلمات المرور مشفرة بالفعل"""
        try:
            with self.db_manager:
                query = "SELECT UserID, Username, Password FROM Users LIMIT 1"
                user = self.db_manager.fetch_one(query)
                
                if not user:
                    return False
                    
                # فحص ما إذا كانت كلمة المرور تبدو مشفرة (bcrypt hash يبدأ بـ $2b$)
                return user.Password.startswith('$2b$')
                
        except Exception as e:
            logger.error(f"خطأ في فحص تشفير كلمات المرور: {e}")
            return False
    
    def upgrade_passwords(self):
        """ترقية كلمات المرور لتصبح مشفرة"""
        try:
            logger.info("بدء ترقية كلمات المرور...")
            
            with self.db_manager:
                # جلب جميع المستخدمين
                query = "SELECT UserID, Username, Password FROM Users"
                users = self.db_manager.fetch_all(query)
                
                if not users:
                    logger.info("لا توجد مستخدمين للترقية")
                    return True
                
                upgraded_count = 0
                
                for user in users:
                    try:
                        # فحص ما إذا كانت كلمة المرور مشفرة بالفعل
                        if user.Password.startswith('$2b$'):
                            logger.debug(f"كلمة مرور المستخدم {user.Username} مشفرة بالفعل")
                            continue
                        
                        # تشفير كلمة المرور
                        hashed_password = self.security.hash_password(user.Password)
                        
                        # تحديث قاعدة البيانات
                        update_query = "UPDATE Users SET Password = ? WHERE UserID = ?"
                        self.db_manager.execute_query(update_query, (hashed_password, user.UserID))
                        
                        logger.info(f"تم تشفير كلمة مرور المستخدم: {user.Username}")
                        upgraded_count += 1
                        
                    except Exception as e:
                        logger.error(f"فشل في تشفير كلمة مرور المستخدم {user.Username}: {e}")
                        continue
                
                logger.info(f"تم ترقية {upgraded_count} كلمة مرور")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في ترقية كلمات المرور: {e}")
            return False
    
    def add_default_admin(self):
        """إضافة مستخدم admin افتراضي إذا لم يكن موجوداً"""
        try:
            with self.db_manager:
                # فحص وجود مستخدم admin
                query = "SELECT * FROM Users WHERE Username = 'admin'"
                admin_user = self.db_manager.fetch_one(query)
                
                if admin_user:
                    logger.info("المستخدم admin موجود بالفعل")
                    return True
                
                # إضافة مستخدم admin
                hashed_password = self.security.hash_password('admin123')
                insert_query = """
                INSERT INTO Users (Username, Password, FullName, UserType, IsActive)
                VALUES (?, ?, ?, ?, 1)
                """
                
                self.db_manager.execute_query(insert_query, (
                    'admin', 
                    hashed_password, 
                    'مدير النظام', 
                    'admin'
                ))
                
                logger.info("تم إضافة المستخدم admin الافتراضي")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في إضافة المستخدم الافتراضي: {e}")
            return False
    
    def run_upgrade(self):
        """تشغيل عملية الترقية الكاملة"""
        logger.info("=" * 50)
        logger.info("بدء ترقية قاعدة البيانات")
        logger.info("=" * 50)
        
        try:
            # إنشاء نسخة احتياطية
            if not self.create_backup():
                logger.error("فشل في إنشاء النسخة الاحتياطية")
                return False
            
            # فحص حالة التشفير
            if self.check_password_encryption():
                logger.info("كلمات المرور مشفرة بالفعل")
            else:
                # ترقية كلمات المرور
                if not self.upgrade_passwords():
                    logger.error("فشل في ترقية كلمات المرور")
                    return False
            
            # إضافة المستخدم الافتراضي
            if not self.add_default_admin():
                logger.error("فشل في إضافة المستخدم الافتراضي")
                return False
            
            logger.info("=" * 50)
            logger.info("تمت ترقية قاعدة البيانات بنجاح!")
            logger.info("=" * 50)
            return True
            
        except Exception as e:
            logger.error(f"خطأ في عملية الترقية: {e}")
            return False

def main():
    # فحص وضع التشغيل التلقائي
    auto_mode = len(sys.argv) > 1 and sys.argv[1] == '--auto'

    if not auto_mode:
        print("ترقية قاعدة البيانات - نظام مركز المخزون")
        print("=" * 50)

    upgrader = DatabaseUpgrader()

    if not auto_mode:
        # تأكيد من المستخدم
        response = input("هل تريد المتابعة مع ترقية قاعدة البيانات؟ (y/n): ")
        if response.lower() not in ['y', 'yes', 'نعم']:
            print("تم إلغاء العملية")
            return

    success = upgrader.run_upgrade()

    if success:
        if not auto_mode:
            print("\n✅ تمت الترقية بنجاح!")
            print("يمكنك الآن تشغيل البرنامج بأمان")
    else:
        if not auto_mode:
            print("\n❌ فشلت عملية الترقية!")
            print("راجع ملف upgrade.log للتفاصيل")
        else:
            print("❌ فشل في ترقية قاعدة البيانات - راجع upgrade.log")

    if not auto_mode:
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
