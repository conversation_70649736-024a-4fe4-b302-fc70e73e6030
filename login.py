
import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttb
import sqlite3
import os

class LoginScreen(ttb.Window):
    def __init__(self):
        super().__init__(themename="pulse")
        self.title("مركز المخزون - تسجيل الدخول")
        self.geometry("400x300")
        
        # إعداد قاعدة البيانات
        self.db_path = os.path.join(os.path.dirname(__file__), 'flour_mill_inventory.db')
        
        self.create_widgets()
        self.center_window()

    def create_widgets(self):
        # إطار رئيسي
        main_frame = ttb.Frame(self)
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)

        # عنوان
        title_label = ttb.Label(main_frame, text="مركز المخزون", font=("Arial", 20, "bold"))
        title_label.pack(pady=20)

        # اسم المستخدم
        ttb.Label(main_frame, text="اسم المستخدم:").pack(pady=5)
        self.username_entry = ttb.Entry(main_frame, width=20)
        self.username_entry.pack(pady=5)

        # كلمة المرور
        ttb.Label(main_frame, text="كلمة المرور:").pack(pady=5)
        self.password_entry = ttb.Entry(main_frame, show="*", width=20)
        self.password_entry.pack(pady=5)

        # زر تسجيل الدخول
        login_btn = ttb.Button(main_frame, text="تسجيل الدخول", command=self.login)
        login_btn.pack(pady=20)

        # ربط Enter بتسجيل الدخول
        self.bind('<Return>', lambda e: self.login())

    def center_window(self):
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # التحقق من المستخدم
        user = self.authenticate_user(username, password)
        
        if user:
            messagebox.showinfo("نجح", f"مرحباً {user['full_name']}")
            self.destroy()
            
            # فتح التطبيق الرئيسي
            try:
                from main_app_simple import MainApplication
                app = MainApplication(user)
                app.mainloop()
            except ImportError:
                messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

    def authenticate_user(self, username, password):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT UserID, FullName, UserType FROM Users WHERE Username = ? AND Password = ?", 
                          (username, password))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'user_id': result[0],
                    'full_name': result[1],
                    'user_type': result[2],
                    'username': username
                }
            return None
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return None

if __name__ == "__main__":
    app = LoginScreen()
    app.mainloop()
