import sqlite3
from datetime import datetime

def fix_database_schema():
    """إصلاح هيكل قاعدة البيانات"""
    try:
        conn = sqlite3.connect("flour_mill_inventory.db")
        cursor = conn.cursor()
        
        # التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(Users)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"الأعمدة الموجودة: {columns}")
        
        # إضافة الأعمدة المفقودة
        if 'CreatedDate' not in columns:
            cursor.execute("ALTER TABLE Users ADD COLUMN CreatedDate TEXT")
            print("✅ تم إضافة عمود CreatedDate")
        
        if 'IsActive' not in columns:
            cursor.execute("ALTER TABLE Users ADD COLUMN IsActive BOOLEAN DEFAULT 1")
            print("✅ تم إضافة عمود IsActive")
        
        # تحديث البيانات الموجودة
        cursor.execute("""
            UPDATE Users 
            SET CreatedDate = ?, IsActive = 1 
            WHERE CreatedDate IS NULL
        """, (datetime.now().strftime('%Y-%m-%d %H:%M:%S'),))
        
        conn.commit()
        conn.close()
        print("✅ تم إصلاح هيكل قاعدة البيانات!")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")

if __name__ == "__main__":
    fix_database_schema()