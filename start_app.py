import tkinter as tk
from tkinter import messagebox, ttk
import sqlite3
import os

def check_database():
    """التحقق من قاعدة البيانات وإنشاؤها إذا لم تكن موجودة"""
    db_path = "flour_mill_inventory.db"
    
    if not os.path.exists(db_path):
        print("إنشاء قاعدة البيانات...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
        CREATE TABLE Users (
            UserID INTEGER PRIMARY KEY AUTOINCREMENT,
            Username TEXT NOT NULL UNIQUE,
            Password TEXT NOT NULL,
            FullName TEXT NOT NULL,
            UserType TEXT NOT NULL
        )
        """)
        
        cursor.execute("""
        INSERT INTO Users (Username, Password, FullName, UserType)
        VALUES ('test', '123', 'مستخدم تجريبي', 'User')
        """)
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء قاعدة البيانات")

def authenticate_user(username, password):
    """التحقق من المستخدم"""
    try:
        conn = sqlite3.connect("flour_mill_inventory.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT UserID, FullName, UserType FROM Users WHERE Username = ? AND Password = ?", 
                      (username, password))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'user_id': result[0],
                'full_name': result[1],
                'user_type': result[2],
                'username': username
            }
        return None
    except Exception as e:
        print(f"خطأ في المصادقة: {e}")
        return None

class MainApp:
    def __init__(self, user):
        self.user = user
        self.root = tk.Tk()
        self.root.title("مركز المخزون - التطبيق الرئيسي")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        self.create_widgets()
        
    def create_widgets(self):
        # شريط علوي
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(header_frame, text="مركز المخزون", 
                              font=("Arial", 18, "bold"), 
                              bg='#2c3e50', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        # معلومات المستخدم
        user_label = tk.Label(header_frame, 
                             text=f"مرحباً، {self.user['full_name']}", 
                             font=("Arial", 12), 
                             bg='#2c3e50', fg='white')
        user_label.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # المحتوى الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رسالة ترحيب
        welcome_label = tk.Label(main_frame, 
                                text="مرحباً بك في نظام إدارة مخزون المطحنة", 
                                font=("Arial", 16, "bold"), 
                                bg='#f0f0f0', fg='#2c3e50')
        welcome_label.pack(pady=30)
        
        # الأزرار الرئيسية
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=20)
        
        # أزرار الوظائف
        buttons = [
            ("المواد الخام", self.show_materials),
            ("المنتجات الجاهزة", self.show_products),
            ("الخلطات", self.show_mixtures),
            ("التقارير", self.show_reports),
            ("المستخدمين", self.show_users),
            ("تسجيل الخروج", self.logout)
        ]
        
        for i, (text, command) in enumerate(buttons):
            row = i // 3
            col = i % 3
            
            btn = tk.Button(buttons_frame, text=text, command=command,
                           width=15, height=3, font=("Arial", 12),
                           bg='#3498db', fg='white', relief=tk.RAISED)
            btn.grid(row=row, column=col, padx=10, pady=10)
        
        # معلومات النظام
        info_frame = tk.Frame(main_frame, bg='#ecf0f1', relief=tk.SUNKEN, bd=1)
        info_frame.pack(fill=tk.X, pady=20)
        
        info_label = tk.Label(info_frame, 
                             text="نظام إدارة مخزون المطحنة - الإصدار 1.0\nتم تطويره لإدارة المواد الخام والمنتجات والخلطات", 
                             font=("Arial", 10), 
                             bg='#ecf0f1', fg='#7f8c8d')
        info_label.pack(pady=10)
    
    def show_materials(self):
        messagebox.showinfo("المواد الخام", "ستتوفر إدارة المواد الخام في الإصدار القادم")
    
    def show_products(self):
        messagebox.showinfo("المنتجات الجاهزة", "ستتوفر إدارة المنتجات الجاهزة في الإصدار القادم")
    
    def show_mixtures(self):
        messagebox.showinfo("الخلطات", "ستتوفر إدارة الخلطات في الإصدار القادم")
    
    def show_reports(self):
        messagebox.showinfo("التقارير", "ستتوفر التقارير في الإصدار القادم")
    
    def show_users(self):
        messagebox.showinfo("المستخدمين", "ستتوفر إدارة المستخدمين في الإصدار القادم")
    
    def logout(self):
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟"):
            self.root.destroy()
            # إعادة فتح شاشة تسجيل الدخول
            app = SimpleLoginApp()
            app.run()
    
    def run(self):
        self.root.mainloop()

class SimpleLoginApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مركز المخزون - تسجيل الدخول")
        self.root.geometry("300x200")
        self.root.configure(bg='white')
        
        # التحقق من قاعدة البيانات
        check_database()
        
        self.create_widgets()
        
    def create_widgets(self):
        # عنوان
        title = tk.Label(self.root, text="مركز المخزون", font=("Arial", 16, "bold"), bg='white')
        title.pack(pady=20)
        
        # اسم المستخدم
        tk.Label(self.root, text="اسم المستخدم:", bg='white').pack()
        self.username_entry = tk.Entry(self.root)
        self.username_entry.pack(pady=5)
        
        # كلمة المرور
        tk.Label(self.root, text="كلمة المرور:", bg='white').pack()
        self.password_entry = tk.Entry(self.root, show="*")
        self.password_entry.pack(pady=5)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(self.root, text="تسجيل الدخول", command=self.login, bg='#4CAF50', fg='white')
        login_btn.pack(pady=20)
        
        # تلميح
        hint = tk.Label(self.root, text="المستخدم: test | كلمة المرور: 123", font=("Arial", 8), bg='white', fg='gray')
        hint.pack()
        
    def login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        user = authenticate_user(username, password)
        
        if user:
            messagebox.showinfo("نجح", f"مرحباً {user['full_name']}\nتم تسجيل الدخول بنجاح!")
            self.root.destroy()
            
            # فتح التطبيق الرئيسي
            main_app = MainApp(user)
            main_app.run()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleLoginApp()
    app.run()



