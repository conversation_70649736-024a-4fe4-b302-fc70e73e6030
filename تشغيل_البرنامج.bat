@echo off
chcp 65001 >nul
title مركز المخزون - تشغيل البرنامج
color 0A

echo ========================================
echo        مركز المخزون - تشغيل البرنامج
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo جاري التحقق من المكتبات المطلوبة...

REM التحقق من المكتبات الأساسية
python -c "import tkinter" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ مكتبة tkinter مفقودة
    echo يرجى إعادة تثبيت Python مع تحديد "tcl/tk and IDLE"
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة إذا لم تكن موجودة
echo جاري تثبيت المكتبات المطلوبة...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تثبيت بعض المكتبات
    echo جاري المحاولة مع خيارات إضافية...
    python -m pip install -r requirements.txt --user --upgrade
)

echo.
echo ✅ تم تثبيت المكتبات بنجاح
echo.
echo جاري فحص قاعدة البيانات وترقيتها إذا لزم الأمر...
python upgrade_database.py --auto

echo.
echo جاري تشغيل البرنامج...
echo.

REM تشغيل البرنامج الرئيسي
python main.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo للحصول على مزيد من التفاصيل، شغل الأمر التالي:
    echo python main.py
    echo.
)

pause
