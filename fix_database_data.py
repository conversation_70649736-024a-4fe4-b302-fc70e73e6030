import sqlite3
from datetime import datetime

def fix_database():
    """إصلاح قاعدة البيانات وإضافة بيانات تجريبية"""
    try:
        conn = sqlite3.connect("flour_mill_inventory.db")
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Users (
                UserID INTEGER PRIMARY KEY AUTOINCREMENT,
                Username TEXT UNIQUE NOT NULL,
                Password TEXT NOT NULL,
                FullName TEXT NOT NULL,
                UserType TEXT DEFAULT 'User',
                CreatedDate TEXT,
                IsActive BOOLEAN DEFAULT 1
            )
        """)
        
        # حذف البيانات القديمة وإضافة بيانات جديدة
        cursor.execute("DELETE FROM Users")
        
        # إضافة مستخدمين تجريبيين
        users_data = [
            ("test", "123", "مستخدم تجريبي", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
            ("admin", "admin", "مدير النظام", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
            ("user1", "123456", "أحمد محمد", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
            ("user2", "123456", "فاطمة علي", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
            ("user3", "123456", "محمد حسن", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 0)
        ]
        
        cursor.executemany("""
            INSERT INTO Users (Username, Password, FullName, UserType, CreatedDate, IsActive)
            VALUES (?, ?, ?, ?, ?, ?)
        """, users_data)
        
        conn.commit()
        conn.close()
        
        print("✅ تم إصلاح قاعدة البيانات وإضافة البيانات التجريبية!")
        
        # التحقق من البيانات
        conn = sqlite3.connect("flour_mill_inventory.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Users")
        count = cursor.fetchone()[0]
        print(f"📊 عدد المستخدمين في قاعدة البيانات: {count}")
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")

if __name__ == "__main__":
    fix_database()