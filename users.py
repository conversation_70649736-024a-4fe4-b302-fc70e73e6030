#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import sys

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, UserManager, DatabaseError

class UsersScreen(ttb.Frame):
    def __init__(self, parent, db_manager, user_manager=None):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.user_manager = user_manager or UserManager(db_manager)
        self.selected_user_id = None
        
        # إنشاء واجهة المستخدم
        self.create_widgets()
        
        # تحميل قائمة المستخدمين
        self.refresh_users_list()
    
    def create_widgets(self):
        # إطار رئيسي
        main_frame = ttb.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان الصفحة
        title_label = ui.RTLLabel(
            main_frame, 
            text="إدارة المستخدمين", 
            font=("Tajawal", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(anchor="e", pady=(0, 20))
        
        # إطار أزرار العمليات
        actions_frame = ttb.Frame(main_frame)
        actions_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار العمليات
        add_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("إضافة مستخدم جديد"),
            command=self.add_user,
            bootstyle="success",
            width=15
        )
        add_btn.pack(side=tk.RIGHT, padx=5)
        
        edit_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("تعديل المستخدم"),
            command=self.edit_user,
            bootstyle="warning",
            width=15
        )
        edit_btn.pack(side=tk.RIGHT, padx=5)
        
        delete_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("حذف المستخدم"),
            command=self.delete_user,
            bootstyle="danger",
            width=15
        )
        delete_btn.pack(side=tk.RIGHT, padx=5)
        
        refresh_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("تحديث القائمة"),
            command=self.refresh_users_list,
            bootstyle="info",
            width=15
        )
        refresh_btn.pack(side=tk.RIGHT, padx=5)
        
        # إطار قائمة المستخدمين
        list_frame = ttb.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء قائمة المستخدمين
        self.create_users_list(list_frame)
    
    def create_users_list(self, parent):
        # عنوان الجدول
        list_title = ui.RTLLabel(
            parent, 
            text="قائمة المستخدمين", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        list_title.pack(anchor="e", pady=(0, 10))
        
        # إطار الجدول مع شريط التمرير
        table_frame = ttb.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # أعمدة الجدول
        columns = ("id", "username", "fullname", "usertype", "lastlogin", "isactive")
        headers = ("م", "اسم المستخدم", "الاسم الكامل", "نوع المستخدم", "آخر دخول", "نشط")
        
        # إنشاء جدول المستخدمين
        self.users_table = ui.RTLTreeview(
            table_frame,
            columns=columns,
            headers=headers
        )
        
        # تحديد عرض الأعمدة
        self.users_table.column("#0", width=0, stretch=tk.NO)
        self.users_table.column("id", width=50, anchor=tk.CENTER)
        self.users_table.column("username", width=120, anchor=tk.CENTER)
        self.users_table.column("fullname", width=150, anchor=tk.CENTER)
        self.users_table.column("usertype", width=100, anchor=tk.CENTER)
        self.users_table.column("lastlogin", width=150, anchor=tk.CENTER)
        self.users_table.column("isactive", width=80, anchor=tk.CENTER)
        
        # ربط حدث النقر المزدوج
        self.users_table.bind("<Double-1>", self.on_user_double_click)
        self.users_table.bind("<<TreeviewSelect>>", self.on_user_select)
        
        self.users_table.pack(fill=tk.BOTH, expand=True)
    
    def refresh_users_list(self):
        """تحديث قائمة المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_table.get_children():
                self.users_table.delete(item)
            
            # جلب المستخدمين من قاعدة البيانات
            users = self.user_manager.get_all_users()
            
            # إضافة المستخدمين إلى الجدول
            for user in users:
                # تنسيق آخر تسجيل دخول
                last_login = ""
                if hasattr(user, 'LastLogin') and user.LastLogin:
                    last_login = user.LastLogin.strftime("%Y-%m-%d %H:%M")
                
                # تنسيق حالة النشاط
                is_active = "نعم" if user.IsActive else "لا"
                
                self.users_table.insert("", tk.END, values=(
                    user.UserID,
                    user.Username,
                    user.FullName,
                    user.UserType,
                    last_login,
                    is_active
                ))
                
        except DatabaseError as e:
            ui.arabic_messagebox("خطأ في قاعدة البيانات", str(e), "error")
        except Exception as e:
            ui.arabic_messagebox("خطأ", f"حدث خطأ في تحديث قائمة المستخدمين: {str(e)}", "error")
    
    def on_user_select(self, event):
        """عند تحديد مستخدم من القائمة"""
        selection = self.users_table.selection()
        if selection:
            item = self.users_table.item(selection[0])
            self.selected_user_id = item['values'][0]
    
    def on_user_double_click(self, event):
        """عند النقر المزدوج على مستخدم"""
        self.edit_user()
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self, "إضافة مستخدم جديد")
        if dialog.result:
            try:
                success = self.user_manager.add_user(
                    dialog.result['username'],
                    dialog.result['password'],
                    dialog.result['fullname'],
                    dialog.result['usertype']
                )
                
                if success:
                    ui.arabic_messagebox("نجاح", "تم إضافة المستخدم بنجاح", "info")
                    self.refresh_users_list()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في إضافة المستخدم", "error")
                    
            except DatabaseError as e:
                ui.arabic_messagebox("خطأ في قاعدة البيانات", str(e), "error")
            except Exception as e:
                ui.arabic_messagebox("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def edit_user(self):
        """تعديل المستخدم المحدد"""
        if not self.selected_user_id:
            ui.arabic_messagebox("تنبيه", "يرجى تحديد مستخدم للتعديل", "warning")
            return
        
        try:
            # جلب بيانات المستخدم
            user = self.user_manager.get_user_by_id(self.selected_user_id)
            if not user:
                ui.arabic_messagebox("خطأ", "لم يتم العثور على المستخدم", "error")
                return
            
            # فتح نافذة التعديل
            dialog = UserDialog(self, "تعديل المستخدم", user)
            if dialog.result:
                success = self.user_manager.update_user(
                    self.selected_user_id,
                    dialog.result['password'],
                    dialog.result['fullname'],
                    dialog.result['usertype'],
                    dialog.result['isactive']
                )
                
                if success:
                    ui.arabic_messagebox("نجاح", "تم تحديث المستخدم بنجاح", "info")
                    self.refresh_users_list()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في تحديث المستخدم", "error")
                    
        except DatabaseError as e:
            ui.arabic_messagebox("خطأ في قاعدة البيانات", str(e), "error")
        except Exception as e:
            ui.arabic_messagebox("خطأ", f"حدث خطأ: {str(e)}", "error")
    
    def delete_user(self):
        """حذف المستخدم المحدد"""
        if not self.selected_user_id:
            ui.arabic_messagebox("تنبيه", "يرجى تحديد مستخدم للحذف", "warning")
            return
        
        # تأكيد الحذف
        if not ui.arabic_messagebox(
            "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذا المستخدم؟\nهذا الإجراء لا يمكن التراجع عنه.",
            "yesno"
        ):
            return
        
        try:
            success = self.user_manager.delete_user(self.selected_user_id)
            
            if success:
                ui.arabic_messagebox("نجاح", "تم حذف المستخدم بنجاح", "info")
                self.refresh_users_list()
                self.selected_user_id = None
            else:
                ui.arabic_messagebox("خطأ", "فشل في حذف المستخدم", "error")
                
        except DatabaseError as e:
            ui.arabic_messagebox("خطأ في قاعدة البيانات", str(e), "error")
        except Exception as e:
            ui.arabic_messagebox("خطأ", f"حدث خطأ: {str(e)}", "error")

class UserDialog:
    """نافذة حوار لإضافة/تعديل المستخدمين"""
    
    def __init__(self, parent, title, user=None):
        self.result = None
        self.user = user
        
        # إنشاء النافذة
        self.dialog = ttb.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x350")
        self.dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # تمركز النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_widgets()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """تمركز النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        main_frame = ttb.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # اسم المستخدم
        username_frame = ttb.Frame(main_frame)
        username_frame.pack(fill=tk.X, pady=5)
        
        username_label = ui.RTLLabel(username_frame, text="اسم المستخدم:")
        username_label.pack(side=tk.RIGHT, padx=5)
        
        self.username_entry = ttb.Entry(username_frame, width=25)
        self.username_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        
        # كلمة المرور
        password_frame = ttb.Frame(main_frame)
        password_frame.pack(fill=tk.X, pady=5)
        
        password_label = ui.RTLLabel(password_frame, text="كلمة المرور:")
        password_label.pack(side=tk.RIGHT, padx=5)
        
        self.password_entry = ttb.Entry(password_frame, width=25, show="*")
        self.password_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        
        # الاسم الكامل
        fullname_frame = ttb.Frame(main_frame)
        fullname_frame.pack(fill=tk.X, pady=5)
        
        fullname_label = ui.RTLLabel(fullname_frame, text="الاسم الكامل:")
        fullname_label.pack(side=tk.RIGHT, padx=5)
        
        self.fullname_entry = ttb.Entry(fullname_frame, width=25)
        self.fullname_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        
        # نوع المستخدم
        usertype_frame = ttb.Frame(main_frame)
        usertype_frame.pack(fill=tk.X, pady=5)
        
        usertype_label = ui.RTLLabel(usertype_frame, text="نوع المستخدم:")
        usertype_label.pack(side=tk.RIGHT, padx=5)
        
        self.usertype_var = tk.StringVar(value="مستخدم")
        usertype_combo = ttb.Combobox(
            usertype_frame, 
            textvariable=self.usertype_var,
            values=["مدير", "مستخدم", "مشرف"],
            state="readonly",
            width=22
        )
        usertype_combo.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        
        # حالة النشاط (للتعديل فقط)
        if self.user:
            active_frame = ttb.Frame(main_frame)
            active_frame.pack(fill=tk.X, pady=5)
            
            active_label = ui.RTLLabel(active_frame, text="نشط:")
            active_label.pack(side=tk.RIGHT, padx=5)
            
            self.active_var = tk.BooleanVar(value=True)
            active_check = ttb.Checkbutton(
                active_frame,
                variable=self.active_var,
                text="المستخدم نشط"
            )
            active_check.pack(side=tk.RIGHT, padx=5)
        
        # أزرار العمليات
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=20)
        
        save_btn = ttb.Button(
            buttons_frame,
            text=ui.format_arabic_text("حفظ"),
            command=self.save_user,
            bootstyle="success",
            width=12
        )
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        cancel_btn = ttb.Button(
            buttons_frame,
            text=ui.format_arabic_text("إلغاء"),
            command=self.cancel,
            bootstyle="secondary",
            width=12
        )
        cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # تعبئة البيانات للتعديل
        if self.user:
            self.username_entry.insert(0, self.user.Username)
            self.username_entry.config(state="readonly")  # منع تعديل اسم المستخدم
            self.fullname_entry.insert(0, self.user.FullName)
            self.usertype_var.set(self.user.UserType)
            if hasattr(self, 'active_var'):
                self.active_var.set(bool(self.user.IsActive))
        
        # التركيز على أول حقل
        if self.user:
            self.password_entry.focus()
        else:
            self.username_entry.focus()
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من البيانات
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        fullname = self.fullname_entry.get().strip()
        usertype = self.usertype_var.get()
        
        if not username or not fullname:
            ui.arabic_messagebox("خطأ", "يرجى إدخال اسم المستخدم والاسم الكامل", "error")
            return
        
        if not self.user and not password:
            ui.arabic_messagebox("خطأ", "يرجى إدخال كلمة المرور", "error")
            return
        
        # إعداد النتيجة
        self.result = {
            'username': username,
            'password': password,
            'fullname': fullname,
            'usertype': usertype,
            'isactive': getattr(self, 'active_var', tk.BooleanVar(value=True)).get()
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

if __name__ == "__main__":
    # اختبار الشاشة
    root = ttb.Window(themename="pulse")
    root.title("اختبار شاشة المستخدمين")
    root.geometry("800x600")
    
    # إنشاء مديري قاعدة البيانات
    db_manager = DatabaseManager()
    user_manager = UserManager(db_manager)
    
    # إنشاء الشاشة
    users_screen = UsersScreen(root, db_manager, user_manager)
    users_screen.pack(fill=tk.BOTH, expand=True)
    
    root.mainloop()
