import pyodbc
import os

def fix_missing_tables():
    """إنشاء الجداول المفقودة"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'flour_mill_inventory.accdb')
    
    try:
        # الاتصال بقاعدة البيانات
        conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};'
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
        
        # إنشاء جدول المنتجات
        try:
            cursor.execute("""
            CREATE TABLE Products (
                ProductID AUTOINCREMENT PRIMARY KEY,
                ProductName TEXT(100) NOT NULL,
                CategoryID LONG,
                SupplierID LONG,
                UnitPrice CURRENCY,
                StockQuantity DOUBLE,
                MinimumStock DOUBLE,
                Description MEMO,
                CreatedDate DATETIME
            )
            """)
            print("✅ تم إنشاء جدول Products")
        except Exception as e:
            print(f"جدول Products موجود بالفعل أو خطأ: {e}")

        # إنشاء جدول معاملات المخزون
        try:
            cursor.execute("""
            CREATE TABLE StockTransactions (
                TransactionID AUTOINCREMENT PRIMARY KEY,
                ProductID LONG NOT NULL,
                TransactionType TEXT(20) NOT NULL,
                Quantity DOUBLE NOT NULL,
                UnitPrice CURRENCY,
                TotalAmount CURRENCY,
                TransactionDate DATETIME,
                UserID LONG,
                Notes MEMO
            )
            """)
            print("✅ تم إنشاء جدول StockTransactions")
        except Exception as e:
            print(f"جدول StockTransactions موجود بالفعل أو خطأ: {e}")

        # إضافة بعض المنتجات التجريبية
        try:
            cursor.execute("""
            INSERT INTO Products (ProductName, CategoryID, SupplierID, UnitPrice, StockQuantity, MinimumStock, Description)
            VALUES ('دقيق أبيض فاخر', 1, 1, 50.0, 100.0, 10.0, 'دقيق أبيض عالي الجودة')
            """)
            cursor.execute("""
            INSERT INTO Products (ProductName, CategoryID, SupplierID, UnitPrice, StockQuantity, MinimumStock, Description)
            VALUES ('نخالة ناعمة', 2, 1, 25.0, 50.0, 5.0, 'نخالة ناعمة للاستخدام المنزلي')
            """)
            cursor.execute("""
            INSERT INTO Products (ProductName, CategoryID, SupplierID, UnitPrice, StockQuantity, MinimumStock, Description)
            VALUES ('علف ذرة', 3, 2, 30.0, 75.0, 8.0, 'علف ذرة للحيوانات')
            """)
            print("✅ تم إضافة منتجات تجريبية")
        except Exception as e:
            print(f"المنتجات موجودة بالفعل أو خطأ: {e}")

        # حذف المستخدمين المكررين
        try:
            cursor.execute("DELETE FROM Users WHERE UserID > 1")
            print("✅ تم حذف المستخدمين المكررين")
        except Exception as e:
            print(f"خطأ في حذف المستخدمين المكررين: {e}")

        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("✅ تم إصلاح قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")

if __name__ == "__main__":
    fix_missing_tables()
