
import sqlite3
import os
from datetime import datetime

class DatabaseError(Exception):
    """استثناء خاص بقاعدة البيانات"""
    pass

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.db_path = os.path.join(os.path.dirname(__file__), 'flour_mill_inventory.db')

    def connect(self):
        try:
            self.connection = sqlite3.connect(self.db_path)
            return True
        except Exception as e:
            print(f'خطأ في الاتصال: {e}')
            return False

    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            try:
                self.connection.close()
            except:
                pass
            self.connection = None

    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        if self.connection:
            return self.connection
        # إنشاء اتصال جديد إذا لم يكن موجود<|im_start|>
        try:
            return sqlite3.connect(self.db_path)
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None

    def execute_query(self, query, params=None):
        """تنفيذ استعلام وإرجاع النتائج"""
        conn = self.get_connection()
        if not conn:
            return None

        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # إذا كان الاستعلام SELECT، إرجاع النتائج
            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                return results
            else:
                # إذا كان INSERT/UPDATE/DELETE، حفظ التغييرات
                conn.commit()
                return True

        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None

    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        query = "SELECT UserID, FullName, UserType FROM Users WHERE Username = ? AND Password = ?"
        result = self.execute_query(query, (username, password))

        if result and len(result) > 0:
            user_data = result[0]
            return {
                'user_id': user_data[0],
                'full_name': user_data[1],
                'user_type': user_data[2],
                'username': username
            }
        return None

    def get_all_products(self):
        """الحصول على جميع المنتجات"""
        query = "SELECT * FROM Products ORDER BY ProductName"
        return self.execute_query(query)

    def get_all_categories(self):
        """الحصول على جميع الفئات"""
        query = "SELECT * FROM Categories ORDER BY CategoryName"
        return self.execute_query(query)

    def get_all_suppliers(self):
        """الحصول على جميع الموردين"""
        query = "SELECT * FROM Suppliers ORDER BY SupplierName"
        return self.execute_query(query)

    def add_product(self, product_data):
        """إضافة منتج جديد"""
        query = """
        INSERT INTO Products (ProductName, CategoryID, SupplierID, UnitPrice,
                            StockQuantity, MinimumStock, Description)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return self.execute_query(query, product_data)

    def update_product(self, product_id, product_data):
        """تحديث بيانات منتج"""
        query = """
        UPDATE Products
        SET ProductName = ?, CategoryID = ?, SupplierID = ?, UnitPrice = ?,
            StockQuantity = ?, MinimumStock = ?, Description = ?
        WHERE ProductID = ?
        """
        params = product_data + (product_id,)
        return self.execute_query(query, params)

    def delete_product(self, product_id):
        """حذف منتج"""
        query = "DELETE FROM Products WHERE ProductID = ?"
        return self.execute_query(query, (product_id,))

    def get_low_stock_products(self):
        """الحصول على المنتجات ذات المخزون المنخفض"""
        query = """
        SELECT p.*, c.CategoryName, s.SupplierName
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Suppliers s ON p.SupplierID = s.SupplierID
        WHERE p.StockQuantity <= p.MinimumStock
        ORDER BY p.ProductName
        """
        return self.execute_query(query)

    def record_transaction(self, transaction_data):
        """تسجيل معاملة مخزون"""
        query = """
        INSERT INTO StockTransactions (ProductID, TransactionType, Quantity,
                                     UnitPrice, TotalAmount, TransactionDate,
                                     UserID, Notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        return self.execute_query(query, transaction_data)


class SecurityManager:
    """مدير الأمان لتشفير كلمات المرور"""

    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور"""
        try:
            import bcrypt
            # تحويل كلمة المرور إلى bytes
            password_bytes = password.encode('utf-8')
            # إنشاء hash
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password_bytes, salt)
            return hashed.decode('utf-8')
        except Exception as e:
            print(f"خطأ في تشفير كلمة المرور: {e}")
            return password  # إرجاع كلمة المرور كما هي في حالة الخطأ

    @staticmethod
    def verify_password(password, hashed_password):
        """التحقق من كلمة المرور"""
        try:
            import bcrypt
            # تحويل كلمة المرور إلى bytes
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except Exception as e:
            print(f"خطأ في التحقق من كلمة المرور: {e}")
            # في حالة الخطأ، مقارنة نصية بسيطة
            return password == hashed_password


class UserManager:
    """مدير المستخدمين"""

    def __init__(self, db_manager):
        self.db = db_manager
        self.security = SecurityManager()

    def authenticate(self, username, password):
        """مصادقة المستخدم"""
        try:
            query = "SELECT UserID, Username, Password, FullName, UserType FROM Users WHERE Username = ?"
            result = self.db.execute_query(query, (username,))

            if result and len(result) > 0:
                user_data = result[0]
                stored_password = user_data[2]

                # التحقق من كلمة المرور - محاولة الطرق المختلفة
                password_match = False

                # أولاً: محاولة المقارنة المباشرة (للكلمات البسيطة)
                if password == stored_password:
                    password_match = True
                else:
                    # ثانياً: محاولة التحقق من كلمة المرور المشفرة
                    try:
                        if self.security.verify_password(password, stored_password):
                            password_match = True
                    except:
                        pass  # تجاهل أخطاء التشفير

                if password_match:
                    return {
                        'user_id': user_data[0],
                        'username': user_data[1],
                        'full_name': user_data[3],
                        'user_type': user_data[4],
                        'is_active': True
                    }
            return None
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return None

    def authenticate_user(self, username, password):
        """مصادقة المستخدم - اسم بديل للتوافق"""
        return self.authenticate(username, password)

    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            query = """
            SELECT UserID, Username, FullName, UserType, LastLogin, IsActive
            FROM Users
            ORDER BY FullName
            """
            result = self.db.execute_query(query)

            if result:
                users = []
                for row in result:
                    user = type('User', (), {
                        'UserID': row[0],
                        'Username': row[1],
                        'FullName': row[2],
                        'UserType': row[3],
                        'LastLogin': row[4] if len(row) > 4 else None,
                        'IsActive': row[5] if len(row) > 5 else True
                    })()
                    users.append(user)
                return users
            return []
        except Exception as e:
            print(f"خطأ في جلب المستخدمين: {e}")
            return []

    def add_user(self, username, password, fullname, usertype, is_active=True):
        """إضافة مستخدم جديد"""
        try:
            # تشفير كلمة المرور
            hashed_password = self.security.hash_password(password)

            query = """
            INSERT INTO Users (Username, Password, FullName, UserType, IsActive, CreatedDate)
            VALUES (?, ?, ?, ?, ?, ?)
            """

            result = self.db.execute_query(query, (
                username, hashed_password, fullname, usertype, is_active, datetime.now()
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في إضافة المستخدم: {e}")
            raise DatabaseError(f"فشل في إضافة المستخدم: {str(e)}")

    def update_user(self, user_id, username, fullname, usertype, is_active=True, password=None):
        """تحديث بيانات المستخدم"""
        try:
            if password:
                # تحديث مع كلمة المرور
                hashed_password = self.security.hash_password(password)
                query = """
                UPDATE Users
                SET Username = ?, Password = ?, FullName = ?, UserType = ?, IsActive = ?
                WHERE UserID = ?
                """
                params = (username, hashed_password, fullname, usertype, is_active, user_id)
            else:
                # تحديث بدون كلمة المرور
                query = """
                UPDATE Users
                SET Username = ?, FullName = ?, UserType = ?, IsActive = ?
                WHERE UserID = ?
                """
                params = (username, fullname, usertype, is_active, user_id)

            result = self.db.execute_query(query, params)
            return result is not None
        except Exception as e:
            print(f"خطأ في تحديث المستخدم: {e}")
            raise DatabaseError(f"فشل في تحديث المستخدم: {str(e)}")

    def delete_user(self, user_id):
        """حذف مستخدم"""
        try:
            query = "DELETE FROM Users WHERE UserID = ?"
            result = self.db.execute_query(query, (user_id,))
            return result is not None
        except Exception as e:
            print(f"خطأ في حذف المستخدم: {e}")
            raise DatabaseError(f"فشل في حذف المستخدم: {str(e)}")

    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        try:
            query = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE UserID = ?"
            result = self.db.execute_query(query, (user_id,))

            if result and len(result) > 0:
                row = result[0]
                return type('User', (), {
                    'UserID': row[0],
                    'Username': row[1],
                    'FullName': row[2],
                    'UserType': row[3],
                    'IsActive': row[4] if len(row) > 4 else True
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب المستخدم: {e}")
            return None

    def get_user_by_username(self, username):
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        try:
            query = "SELECT UserID, Username, FullName, UserType FROM Users WHERE Username = ?"
            result = self.db.execute_query(query, (username,))

            if result and len(result) > 0:
                row = result[0]
                return {
                    'UserID': row[0],
                    'Username': row[1],
                    'FullName': row[2],
                    'Role': row[3],  # استخدام Role بدلاً من UserType للتوافق
                    'IsActive': True  # افتراضي
                }
            return None
        except Exception as e:
            print(f"خطأ في جلب المستخدم بالاسم: {e}")
            return None

    def create_user(self, username, password, full_name, role='User'):
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم وجود المستخدم
            existing_user = self.get_user_by_username(username)
            if existing_user:
                print(f"المستخدم '{username}' موجود بالفعل")
                return False

            query = """
            INSERT INTO Users (Username, Password, FullName, UserType)
            VALUES (?, ?, ?, ?)
            """

            result = self.db.execute_query(query, (
                username, password, full_name, role
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم: {e}")
            return False


class RawMaterialsManager:
    """مدير المواد الخام"""

    def __init__(self, db_manager):
        self.db = db_manager

    def get_all_materials(self):
        """الحصول على جميع المواد الخام"""
        try:
            query = """
            SELECT MaterialID, MaterialCode, MaterialName, Weight, Quantity, Unit, Description, LastUpdated
            FROM RawMaterials
            ORDER BY MaterialName
            """
            result = self.db.execute_query(query)

            if result:
                materials = []
                for row in result:
                    material = type('Material', (), {
                        'MaterialID': row[0],
                        'MaterialCode': row[1],
                        'MaterialName': row[2],
                        'Weight': row[3] or 0,
                        'Quantity': row[4] or 0,
                        'Unit': row[5] or '',
                        'Description': row[6] or '',
                        'LastUpdated': row[7] if len(row) > 7 else None
                    })()
                    materials.append(material)
                return materials
            return []
        except Exception as e:
            print(f"خطأ في جلب المواد الخام: {e}")
            return []

    def add_material(self, code, name, weight=0, quantity=0, unit='', description=''):
        """إضافة مادة خام جديدة"""
        try:
            query = """
            INSERT INTO RawMaterials (MaterialCode, MaterialName, Weight, Quantity, Unit, Description, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            result = self.db.execute_query(query, (
                code, name, weight, quantity, unit, description, datetime.now()
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في إضافة المادة الخام: {e}")
            raise DatabaseError(f"فشل في إضافة المادة الخام: {str(e)}")

    def update_material(self, material_id, name, weight=0, quantity=0, unit='', description=''):
        """تحديث بيانات المادة الخام"""
        try:
            query = """
            UPDATE RawMaterials
            SET MaterialName = ?, Weight = ?, Quantity = ?, Unit = ?, Description = ?, LastUpdated = ?
            WHERE MaterialID = ?
            """

            result = self.db.execute_query(query, (
                name, weight, quantity, unit, description, datetime.now(), material_id
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في تحديث المادة الخام: {e}")
            raise DatabaseError(f"فشل في تحديث المادة الخام: {str(e)}")

    def delete_material(self, material_id):
        """حذف مادة خام"""
        try:
            query = "DELETE FROM RawMaterials WHERE MaterialID = ?"
            result = self.db.execute_query(query, (material_id,))
            return result is not None
        except Exception as e:
            print(f"خطأ في حذف المادة الخام: {e}")
            raise DatabaseError(f"فشل في حذف المادة الخام: {str(e)}")

    def get_material_by_id(self, material_id):
        """الحصول على مادة خام بواسطة المعرف"""
        try:
            query = """
            SELECT MaterialID, MaterialCode, MaterialName, Weight, Quantity, Unit, Description
            FROM RawMaterials
            WHERE MaterialID = ?
            """
            result = self.db.execute_query(query, (material_id,))

            if result and len(result) > 0:
                row = result[0]
                return type('Material', (), {
                    'MaterialID': row[0],
                    'MaterialCode': row[1],
                    'MaterialName': row[2],
                    'Weight': row[3] or 0,
                    'Quantity': row[4] or 0,
                    'Unit': row[5] or '',
                    'Description': row[6] or ''
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب المادة الخام: {e}")
            return None

    def get_material_by_code(self, code):
        """الحصول على مادة خام بواسطة الرمز"""
        try:
            query = """
            SELECT MaterialID, MaterialCode, MaterialName, Weight, Quantity, Unit, Description
            FROM RawMaterials
            WHERE MaterialCode = ?
            """
            result = self.db.execute_query(query, (code,))

            if result and len(result) > 0:
                row = result[0]
                return type('Material', (), {
                    'MaterialID': row[0],
                    'MaterialCode': row[1],
                    'MaterialName': row[2],
                    'Weight': row[3] or 0,
                    'Quantity': row[4] or 0,
                    'Unit': row[5] or '',
                    'Description': row[6] or ''
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب المادة الخام بالرمز: {e}")
            return None


class FinishedProductsManager:
    """مدير المنتجات الجاهزة"""

    def __init__(self, db_manager):
        self.db = db_manager

    def get_all_products(self):
        """الحصول على جميع المنتجات الجاهزة"""
        try:
            query = """
            SELECT ProductID, ProductCode, ProductName, Weight, Quantity, Unit, Description, LastUpdated
            FROM FinishedProducts
            ORDER BY ProductName
            """
            result = self.db.execute_query(query)

            if result:
                products = []
                for row in result:
                    product = type('Product', (), {
                        'ProductID': row[0],
                        'ProductCode': row[1],
                        'ProductName': row[2],
                        'Weight': row[3] or 0,
                        'Quantity': row[4] or 0,
                        'Unit': row[5] or '',
                        'Description': row[6] or '',
                        'LastUpdated': row[7] if len(row) > 7 else None
                    })()
                    products.append(product)
                return products
            return []
        except Exception as e:
            print(f"خطأ في جلب المنتجات الجاهزة: {e}")
            return []

    def add_product(self, code, name, weight=0, quantity=0, unit='', description=''):
        """إضافة منتج جاهز جديد"""
        try:
            query = """
            INSERT INTO FinishedProducts (ProductCode, ProductName, Weight, Quantity, Unit, Description, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            result = self.db.execute_query(query, (
                code, name, weight, quantity, unit, description, datetime.now()
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في إضافة المنتج الجاهز: {e}")
            raise DatabaseError(f"فشل في إضافة المنتج الجاهز: {str(e)}")

    def update_product(self, product_id, name, weight=0, quantity=0, unit='', description=''):
        """تحديث بيانات المنتج الجاهز"""
        try:
            query = """
            UPDATE FinishedProducts
            SET ProductName = ?, Weight = ?, Quantity = ?, Unit = ?, Description = ?, LastUpdated = ?
            WHERE ProductID = ?
            """

            result = self.db.execute_query(query, (
                name, weight, quantity, unit, description, datetime.now(), product_id
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في تحديث المنتج الجاهز: {e}")
            raise DatabaseError(f"فشل في تحديث المنتج الجاهز: {str(e)}")

    def delete_product(self, product_id):
        """حذف منتج جاهز"""
        try:
            query = "DELETE FROM FinishedProducts WHERE ProductID = ?"
            result = self.db.execute_query(query, (product_id,))
            return result is not None
        except Exception as e:
            print(f"خطأ في حذف المنتج الجاهز: {e}")
            raise DatabaseError(f"فشل في حذف المنتج الجاهز: {str(e)}")

    def get_product_by_id(self, product_id):
        """الحصول على منتج جاهز بواسطة المعرف"""
        try:
            query = """
            SELECT ProductID, ProductCode, ProductName, Weight, Quantity, Unit, Description
            FROM FinishedProducts
            WHERE ProductID = ?
            """
            result = self.db.execute_query(query, (product_id,))

            if result and len(result) > 0:
                row = result[0]
                return type('Product', (), {
                    'ProductID': row[0],
                    'ProductCode': row[1],
                    'ProductName': row[2],
                    'Weight': row[3] or 0,
                    'Quantity': row[4] or 0,
                    'Unit': row[5] or '',
                    'Description': row[6] or ''
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب المنتج الجاهز: {e}")
            return None

    def get_product_by_code(self, code):
        """الحصول على منتج جاهز بواسطة الرمز"""
        try:
            query = """
            SELECT ProductID, ProductCode, ProductName, Weight, Quantity, Unit, Description
            FROM FinishedProducts
            WHERE ProductCode = ?
            """
            result = self.db.execute_query(query, (code,))

            if result and len(result) > 0:
                row = result[0]
                return type('Product', (), {
                    'ProductID': row[0],
                    'ProductCode': row[1],
                    'ProductName': row[2],
                    'Weight': row[3] or 0,
                    'Quantity': row[4] or 0,
                    'Unit': row[5] or '',
                    'Description': row[6] or ''
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب المنتج الجاهز بالرمز: {e}")
            return None


class MixturesManager:
    """مدير الخلطات"""

    def __init__(self, db_manager):
        self.db = db_manager

    def get_all_mixtures(self):
        """الحصول على جميع الخلطات"""
        try:
            query = """
            SELECT MixtureID, MixtureCode, MixtureName, TotalWeight, Description, LastUpdated
            FROM Mixtures
            ORDER BY MixtureName
            """
            result = self.db.execute_query(query)

            if result:
                mixtures = []
                for row in result:
                    mixture = type('Mixture', (), {
                        'MixtureID': row[0],
                        'MixtureCode': row[1],
                        'MixtureName': row[2],
                        'TotalWeight': row[3] or 0,
                        'Description': row[4] or '',
                        'LastUpdated': row[5] if len(row) > 5 else None
                    })()
                    mixtures.append(mixture)
                return mixtures
            return []
        except Exception as e:
            print(f"خطأ في جلب الخلطات: {e}")
            return []

    def add_mixture(self, code, name, total_weight=0, description=''):
        """إضافة خلطة جديدة"""
        try:
            query = """
            INSERT INTO Mixtures (MixtureCode, MixtureName, TotalWeight, Description, LastUpdated)
            VALUES (?, ?, ?, ?, ?)
            """

            result = self.db.execute_query(query, (
                code, name, total_weight, description, datetime.now()
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في إضافة الخلطة: {e}")
            raise DatabaseError(f"فشل في إضافة الخلطة: {str(e)}")

    def update_mixture(self, mixture_id, name, total_weight=0, description=''):
        """تحديث بيانات الخلطة"""
        try:
            query = """
            UPDATE Mixtures
            SET MixtureName = ?, TotalWeight = ?, Description = ?, LastUpdated = ?
            WHERE MixtureID = ?
            """

            result = self.db.execute_query(query, (
                name, total_weight, description, datetime.now(), mixture_id
            ))

            return result is not None
        except Exception as e:
            print(f"خطأ في تحديث الخلطة: {e}")
            raise DatabaseError(f"فشل في تحديث الخلطة: {str(e)}")

    def delete_mixture(self, mixture_id):
        """حذف خلطة"""
        try:
            # حذف مكونات الخلطة أولاً
            self.db.execute_query("DELETE FROM MixtureComponents WHERE MixtureID = ?", (mixture_id,))

            # حذف الخلطة
            query = "DELETE FROM Mixtures WHERE MixtureID = ?"
            result = self.db.execute_query(query, (mixture_id,))
            return result is not None
        except Exception as e:
            print(f"خطأ في حذف الخلطة: {e}")
            raise DatabaseError(f"فشل في حذف الخلطة: {str(e)}")

    def get_mixture_by_id(self, mixture_id):
        """الحصول على خلطة بواسطة المعرف"""
        try:
            query = """
            SELECT MixtureID, MixtureCode, MixtureName, TotalWeight, Description
            FROM Mixtures
            WHERE MixtureID = ?
            """
            result = self.db.execute_query(query, (mixture_id,))

            if result and len(result) > 0:
                row = result[0]
                return type('Mixture', (), {
                    'MixtureID': row[0],
                    'MixtureCode': row[1],
                    'MixtureName': row[2],
                    'TotalWeight': row[3] or 0,
                    'Description': row[4] or ''
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب الخلطة: {e}")
            return None

    def get_mixture_by_code(self, code):
        """الحصول على خلطة بواسطة الرمز"""
        try:
            query = """
            SELECT MixtureID, MixtureCode, MixtureName, TotalWeight, Description
            FROM Mixtures
            WHERE MixtureCode = ?
            """
            result = self.db.execute_query(query, (code,))

            if result and len(result) > 0:
                row = result[0]
                return type('Mixture', (), {
                    'MixtureID': row[0],
                    'MixtureCode': row[1],
                    'MixtureName': row[2],
                    'TotalWeight': row[3] or 0,
                    'Description': row[4] or ''
                })()
            return None
        except Exception as e:
            print(f"خطأ في جلب الخلطة بالرمز: {e}")
            return None

    def get_mixture_components(self, mixture_id):
        """الحصول على مكونات الخلطة"""
        try:
            query = """
            SELECT mc.ComponentID, mc.MaterialID, rm.MaterialName, mc.Quantity, mc.Unit
            FROM MixtureComponents mc
            LEFT JOIN RawMaterials rm ON mc.MaterialID = rm.MaterialID
            WHERE mc.MixtureID = ?
            ORDER BY rm.MaterialName
            """
            result = self.db.execute_query(query, (mixture_id,))

            if result:
                components = []
                for row in result:
                    component = type('Component', (), {
                        'ComponentID': row[0],
                        'MaterialID': row[1],
                        'MaterialName': row[2] or '',
                        'Quantity': row[3] or 0,
                        'Unit': row[4] or ''
                    })()
                    components.append(component)
                return components
            return []
        except Exception as e:
            print(f"خطأ في جلب مكونات الخلطة: {e}")
            return []

    def add_mixture_component(self, mixture_id, material_id, quantity, unit=''):
        """إضافة مكون للخلطة"""
        try:
            query = """
            INSERT INTO MixtureComponents (MixtureID, MaterialID, Quantity, Unit)
            VALUES (?, ?, ?, ?)
            """

            result = self.db.execute_query(query, (mixture_id, material_id, quantity, unit))
            return result is not None
        except Exception as e:
            print(f"خطأ في إضافة مكون الخلطة: {e}")
            raise DatabaseError(f"فشل في إضافة مكون الخلطة: {str(e)}")

    def update_mixture_component(self, component_id, quantity, unit=''):
        """تحديث مكون الخلطة"""
        try:
            query = """
            UPDATE MixtureComponents
            SET Quantity = ?, Unit = ?
            WHERE ComponentID = ?
            """

            result = self.db.execute_query(query, (quantity, unit, component_id))
            return result is not None
        except Exception as e:
            print(f"خطأ في تحديث مكون الخلطة: {e}")
            raise DatabaseError(f"فشل في تحديث مكون الخلطة: {str(e)}")

    def delete_mixture_component(self, component_id):
        """حذف مكون من الخلطة"""
        try:
            query = "DELETE FROM MixtureComponents WHERE ComponentID = ?"
            result = self.db.execute_query(query, (component_id,))
            return result is not None
        except Exception as e:
            print(f"خطأ في حذف مكون الخلطة: {e}")
            raise DatabaseError(f"فشل في حذف مكون الخلطة: {str(e)}")
