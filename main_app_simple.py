#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttb
import os
import sys

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, UserManager, RawMaterialsManager

class MainApplication(ttb.Window):
    def __init__(self, user=None):
        super().__init__(themename="pulse")

        # إعدادات النافذة
        self.title("مركز المخزون - نسخة مبسطة")
        self.geometry("800x600")
        self.minsize(600, 400)

        # حفظ بيانات المستخدم الحالي
        self.current_user = user or {'username': 'مجهول', 'full_name': 'مستخدم مجهول'}

        # إعداد قاعدة البيانات
        self.setup_database()

        # إنشاء واجهة المستخدم
        self.create_widgets()

        # وضع النافذة في وسط الشاشة
        self.center_window()

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            self.user_manager = UserManager(self.db_manager)
            self.materials_manager = RawMaterialsManager(self.db_manager)
            print("✅ تم إعداد قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل في الاتصال بقاعدة البيانات:\n{e}")

    def center_window(self):
        """وضع النافذة في وسط الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttb.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # شريط العنوان
        title_frame = ttb.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # عنوان التطبيق
        title_label = ttb.Label(
            title_frame,
            text="مركز المخزون - نظام إدارة مخزون المطحنة",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=tk.LEFT)

        # معلومات المستخدم
        user_label = ttb.Label(
            title_frame,
            text=f"مرحباً، {self.current_user.get('full_name', 'مستخدم')}",
            font=("Arial", 12),
            bootstyle="secondary"
        )
        user_label.pack(side=tk.RIGHT)

        # إطار المحتوى الرئيسي
        content_frame = ttb.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # رسالة ترحيب
        welcome_label = ttb.Label(
            content_frame,
            text="مرحباً بك في نظام إدارة مخزون المطحنة",
            font=("Arial", 14),
            bootstyle="info"
        )
        welcome_label.pack(pady=50)

        # أزرار الوظائف الأساسية
        buttons_frame = ttb.Frame(content_frame)
        buttons_frame.pack(pady=20)

        # زر إدارة المستخدمين
        users_btn = ttb.Button(
            buttons_frame,
            text="إدارة المستخدمين",
            command=self.show_users,
            bootstyle="primary",
            width=20
        )
        users_btn.pack(side=tk.LEFT, padx=10)

        # زر إدارة المواد الخام
        materials_btn = ttb.Button(
            buttons_frame,
            text="إدارة المواد الخام",
            command=self.show_materials,
            bootstyle="success",
            width=20
        )
        materials_btn.pack(side=tk.LEFT, padx=10)

        # زر إدارة المنتجات النهائية
        products_btn = ttb.Button(
            buttons_frame,
            text="إدارة المنتجات النهائية",
            command=self.show_products,
            bootstyle="warning",
            width=20
        )
        products_btn.pack(side=tk.LEFT, padx=10)

        # زر التقارير
        reports_btn = ttb.Button(
            buttons_frame,
            text="التقارير",
            command=self.show_reports,
            bootstyle="info",
            width=20
        )
        reports_btn.pack(side=tk.LEFT, padx=10)

        # زر الخروج
        exit_btn = ttb.Button(
            buttons_frame,
            text="خروج",
            command=self.quit_app,
            bootstyle="danger",
            width=20
        )
        exit_btn.pack(side=tk.LEFT, padx=10)

        # شريط الحالة
        status_frame = ttb.Frame(main_frame)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(20, 0))

        self.status_label = ttb.Label(
            status_frame,
            text="جاهز",
            bootstyle="secondary"
        )
        self.status_label.pack(side=tk.LEFT)

        # معلومات الإصدار
        version_label = ttb.Label(
            status_frame,
            text="الإصدار 1.0 © 2025 نظام إدارة مخزون المطحنة",
            bootstyle="secondary"
        )
        version_label.pack(side=tk.RIGHT)

    def show_users(self):
        """عرض إدارة المستخدمين"""
        self.status_label.config(text="جاري تحميل إدارة المستخدمين...")
        self.show_users_window()
        self.status_label.config(text="جاهز")

    def show_materials(self):
        """عرض إدارة المواد الخام"""
        self.status_label.config(text="جاري تحميل إدارة المواد الخام...")
        self.show_materials_window()
        self.status_label.config(text="جاهز")

    def show_products(self):
        """عرض إدارة المنتجات النهائية"""
        self.status_label.config(text="جاري تحميل إدارة المنتجات النهائية...")
        self.show_products_window()
        self.status_label.config(text="جاهز")

    def show_reports(self):
        """عرض التقارير"""
        self.status_label.config(text="جاري تحميل التقارير...")
        self.show_reports_window()
        self.status_label.config(text="جاهز")

    def quit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من التطبيق؟"):
            try:
                if hasattr(self, 'db_manager') and self.db_manager:
                    self.db_manager.disconnect()
            except:
                pass
            self.quit()
            self.destroy()

    def show_users_window(self):
        """نافذة إدارة المستخدمين"""
        users_window = ttb.Toplevel(self)
        users_window.title("إدارة المستخدمين")
        users_window.geometry("800x600")
        users_window.grab_set()  # جعل النافذة modal

        # إطار رئيسي
        main_frame = ttb.Frame(users_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = ttb.Label(
            main_frame,
            text="إدارة المستخدمين",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # إطار الأزرار
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))

        # زر إضافة مستخدم
        add_btn = ttb.Button(
            buttons_frame,
            text="إضافة مستخدم",
            command=lambda: self.add_user_dialog(users_window, tree),
            bootstyle="success"
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث
        refresh_btn = ttb.Button(
            buttons_frame,
            text="تحديث",
            command=lambda: self.refresh_users_list(tree),
            bootstyle="info"
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تعديل مستخدم
        edit_btn = ttb.Button(
            buttons_frame,
            text="تعديل مستخدم",
            command=lambda: self.edit_user_dialog(users_window, tree),
            bootstyle="warning"
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر حذف مستخدم
        delete_btn = ttb.Button(
            buttons_frame,
            text="حذف مستخدم",
            command=lambda: self.delete_user(tree),
            bootstyle="danger"
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول المستخدمين
        columns = ("المعرف", "اسم المستخدم", "الاسم الكامل", "النوع")
        tree = ttb.Treeview(main_frame, columns=columns, show="headings", height=15)

        # تعريف الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttb.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول وشريط التمرير
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل البيانات
        self.refresh_users_list(tree)

        # زر الإغلاق
        close_btn = ttb.Button(
            main_frame,
            text="إغلاق",
            command=users_window.destroy,
            bootstyle="secondary"
        )
        close_btn.pack(pady=(20, 0))

    def refresh_users_list(self, tree):
        """تحديث قائمة المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in tree.get_children():
                tree.delete(item)

            # جلب المستخدمين من قاعدة البيانات
            query = "SELECT UserID, Username, FullName, UserType FROM Users ORDER BY FullName"
            users = self.db_manager.execute_query(query)

            if users:
                for user in users:
                    tree.insert("", tk.END, values=(
                        user[0],  # المعرف
                        user[1],  # اسم المستخدم
                        user[2],  # الاسم الكامل
                        user[3]   # النوع
                    ))
            else:
                tree.insert("", tk.END, values=("لا توجد بيانات", "", "", ""))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين:\n{e}")

    def add_user_dialog(self, parent, tree):
        """نافذة إضافة مستخدم جديد"""
        dialog = ttb.Toplevel(parent)
        dialog.title("إضافة مستخدم جديد")
        dialog.geometry("450x550")
        dialog.grab_set()
        dialog.transient(parent)
        dialog.focus_set()
        dialog.resizable(False, False)

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (dialog.winfo_screenheight() // 2) - (550 // 2)
        dialog.geometry(f"450x550+{x}+{y}")

        # إطار رئيسي بسيط
        main_frame = ttb.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان
        title_label = ttb.Label(
            main_frame,
            text="إضافة مستخدم جديد",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # حقول الإدخال
        ttb.Label(main_frame, text="اسم المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        username_entry = ttb.Entry(main_frame, width=30)
        username_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="كلمة المرور:").pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttb.Entry(main_frame, width=30, show="*")
        password_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الاسم الكامل:").pack(anchor=tk.W, pady=(0, 5))
        fullname_entry = ttb.Entry(main_frame, width=30)
        fullname_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="نوع المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        usertype_combo = ttb.Combobox(main_frame, values=["User", "Admin"], state="readonly")
        usertype_combo.set("User")
        usertype_combo.pack(fill=tk.X, pady=(0, 20))

        # إضافة مساحة فارغة
        ttb.Label(main_frame, text="").pack(pady=10)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(30, 20))

        def save_user():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            fullname = fullname_entry.get().strip()
            usertype = usertype_combo.get()

            if not all([username, password, fullname]):
                messagebox.showerror("خطأ", "الرجاء ملء جميع الحقول")
                return

            try:
                # إضافة المستخدم إلى قاعدة البيانات
                success = self.user_manager.create_user(username, password, fullname, usertype)
                if success:
                    messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
                    dialog.destroy()
                    # تحديث قائمة المستخدمين
                    self.refresh_users_list(tree)
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المستخدم")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ:\n{e}")

        # زر الحفظ
        save_btn = ttb.Button(
            buttons_frame,
            text="حفظ",
            command=save_user,
            bootstyle="success",
            width=20
        )
        save_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # زر الإلغاء
        cancel_btn = ttb.Button(
            buttons_frame,
            text="خروج",
            command=dialog.destroy,
            bootstyle="secondary",
            width=20
        )
        cancel_btn.pack(side=tk.RIGHT)

        # تركيز على أول حقل
        username_entry.focus_set()

    def edit_user_dialog(self, parent, tree):
        """نافذة تعديل مستخدم"""
        # التحقق من اختيار مستخدم
        selected_item = tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء اختيار مستخدم للتعديل")
            return

        # الحصول على بيانات المستخدم المحدد
        user_data = tree.item(selected_item[0])['values']
        if not user_data or user_data[0] == "لا توجد بيانات":
            messagebox.showwarning("تحذير", "لا يمكن تعديل هذا العنصر")
            return

        user_id = user_data[0]
        current_username = user_data[1]
        current_fullname = user_data[2]
        current_usertype = user_data[3]

        dialog = ttb.Toplevel(parent)
        dialog.title("تعديل مستخدم")
        dialog.geometry("400x350")
        dialog.grab_set()
        dialog.transient(parent)
        dialog.focus_set()

        # إطار رئيسي
        main_frame = ttb.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال مع القيم الحالية
        ttb.Label(main_frame, text="اسم المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        username_entry = ttb.Entry(main_frame, width=30)
        username_entry.insert(0, current_username)
        username_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):").pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttb.Entry(main_frame, width=30, show="*")
        password_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الاسم الكامل:").pack(anchor=tk.W, pady=(0, 5))
        fullname_entry = ttb.Entry(main_frame, width=30)
        fullname_entry.insert(0, current_fullname)
        fullname_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="نوع المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        usertype_combo = ttb.Combobox(main_frame, values=["User", "Admin"], state="readonly")
        usertype_combo.set(current_usertype)
        usertype_combo.pack(fill=tk.X, pady=(0, 20))

        # أزرار الحفظ والإلغاء
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def update_user():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            fullname = fullname_entry.get().strip()
            usertype = usertype_combo.get()

            if not all([username, fullname]):
                messagebox.showerror("خطأ", "الرجاء ملء الحقول المطلوبة")
                return

            try:
                # تحديث المستخدم في قاعدة البيانات
                if password:  # إذا تم إدخال كلمة مرور جديدة
                    query = """
                    UPDATE Users
                    SET Username=?, Password=?, FullName=?, UserType=?
                    WHERE UserID=?
                    """
                    hashed_password = self.user_manager.hash_password(password)
                    params = (username, hashed_password, fullname, usertype, user_id)
                else:  # الاحتفاظ بكلمة المرور الحالية
                    query = """
                    UPDATE Users
                    SET Username=?, FullName=?, UserType=?
                    WHERE UserID=?
                    """
                    params = (username, fullname, usertype, user_id)

                success = self.db_manager.execute_update(query, params)
                if success:
                    messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح")
                    dialog.destroy()
                    self.refresh_users_list(tree)
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث المستخدم")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ:\n{e}")

        save_btn = ttb.Button(buttons_frame, text="حفظ التعديلات", command=update_user, bootstyle="success")
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttb.Button(buttons_frame, text="إلغاء", command=dialog.destroy, bootstyle="secondary")
        cancel_btn.pack(side=tk.LEFT)

    def delete_user(self, tree):
        """حذف مستخدم"""
        selected_item = tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء اختيار مستخدم للحذف")
            return

        user_data = tree.item(selected_item[0])['values']
        if not user_data or user_data[0] == "لا توجد بيانات":
            messagebox.showwarning("تحذير", "لا يمكن حذف هذا العنصر")
            return

        user_id = user_data[0]
        username = user_data[1]

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم '{username}'؟")
        if result:
            try:
                query = "DELETE FROM Users WHERE UserID=?"
                success = self.db_manager.execute_update(query, (user_id,))
                if success:
                    messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                    self.refresh_users_list(tree)
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المستخدم")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ:\n{e}")

    def show_materials_window(self):
        """نافذة إدارة المواد الخام"""
        materials_window = ttb.Toplevel(self)
        materials_window.title("إدارة المواد الخام")
        materials_window.geometry("900x600")
        materials_window.grab_set()

        # إطار رئيسي
        main_frame = ttb.Frame(materials_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = ttb.Label(
            main_frame,
            text="إدارة المواد الخام",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # إطار الأزرار
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))

        # زر إضافة مادة خام
        add_btn = ttb.Button(
            buttons_frame,
            text="إضافة مادة خام",
            command=lambda: self.add_material_dialog(materials_window, tree),
            bootstyle="success"
        )
        add_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تحديث
        refresh_btn = ttb.Button(
            buttons_frame,
            text="تحديث",
            command=lambda: self.refresh_materials_list(tree),
            bootstyle="info"
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر تعديل مادة خام
        edit_btn = ttb.Button(
            buttons_frame,
            text="تعديل مادة خام",
            command=lambda: self.edit_material_dialog(materials_window, tree),
            bootstyle="warning"
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر حذف مادة خام
        delete_btn = ttb.Button(
            buttons_frame,
            text="حذف مادة خام",
            command=lambda: self.delete_material(tree),
            bootstyle="danger"
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # جدول المواد الخام
        columns = ("المعرف", "اسم المادة", "الكمية", "الوحدة", "السعر", "المورد")
        tree = ttb.Treeview(materials_window, columns=columns, show="headings", height=15)

        # تعريف الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttb.Scrollbar(materials_window, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول وشريط التمرير
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 0), pady=(100, 60))
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(100, 60), padx=(0, 20))

        # تحميل البيانات
        self.refresh_materials_list(tree)

        # زر الإغلاق
        close_btn = ttb.Button(
            main_frame,
            text="إغلاق",
            command=materials_window.destroy,
            bootstyle="secondary"
        )
        close_btn.pack(pady=(20, 0))

    def refresh_materials_list(self, tree):
        """تحديث قائمة المواد الخام"""
        try:
            # مسح البيانات الحالية
            for item in tree.get_children():
                tree.delete(item)

            # جلب المواد الخام من قاعدة البيانات
            query = """
            SELECT MaterialID, MaterialName, Quantity, Unit, Price, Supplier
            FROM RawMaterials
            ORDER BY MaterialName
            """
            materials = self.db_manager.execute_query(query)

            if materials:
                for material in materials:
                    tree.insert("", tk.END, values=(
                        material[0],  # المعرف
                        material[1],  # اسم المادة
                        material[2],  # الكمية
                        material[3],  # الوحدة
                        material[4],  # السعر
                        material[5]   # المورد
                    ))
            else:
                tree.insert("", tk.END, values=("لا توجد مواد خام", "", "", "", "", ""))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المواد الخام:\n{e}")
            # إضافة بيانات تجريبية في حالة عدم وجود جدول
            tree.insert("", tk.END, values=("1", "دقيق أبيض", "100", "كيس", "50.00", "مورد تجريبي"))
            tree.insert("", tk.END, values=("2", "سكر", "50", "كيس", "45.00", "مورد تجريبي"))

    def add_material_dialog(self, parent, tree):
        """نافذة إضافة مادة خام جديدة"""
        dialog = ttb.Toplevel(parent)
        dialog.title("إضافة مادة خام جديدة")
        dialog.geometry("450x550")
        dialog.grab_set()
        dialog.transient(parent)
        dialog.focus_set()
        dialog.resizable(False, False)

        # إطار رئيسي بسيط
        main_frame = ttb.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان
        title_label = ttb.Label(
            main_frame,
            text="إضافة مادة خام جديدة",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # حقول الإدخال
        ttb.Label(main_frame, text="اسم المادة:").pack(anchor=tk.W, pady=(0, 5))
        name_entry = ttb.Entry(main_frame, width=30)
        name_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الكمية:").pack(anchor=tk.W, pady=(0, 5))
        quantity_entry = ttb.Entry(main_frame, width=30)
        quantity_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الوحدة:").pack(anchor=tk.W, pady=(0, 5))
        unit_combo = ttb.Combobox(main_frame, values=["كيس", "كيلو", "طن", "لتر", "قطعة"], state="readonly")
        unit_combo.set("كيس")
        unit_combo.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="السعر:").pack(anchor=tk.W, pady=(0, 5))
        price_entry = ttb.Entry(main_frame, width=30)
        price_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="المورد:").pack(anchor=tk.W, pady=(0, 5))
        supplier_entry = ttb.Entry(main_frame, width=30)
        supplier_entry.pack(fill=tk.X, pady=(0, 20))

        # أزرار الحفظ والإلغاء
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        def save_material():
            name = name_entry.get().strip()
            quantity = quantity_entry.get().strip()
            unit = unit_combo.get()
            price = price_entry.get().strip()
            supplier = supplier_entry.get().strip()

            if not all([name, quantity, price, supplier]):
                messagebox.showerror("خطأ", "الرجاء ملء جميع الحقول")
                return

            try:
                # محاولة تحويل الكمية والسعر إلى أرقام
                float(quantity)
                float(price)

                messagebox.showinfo("نجح", f"تم إضافة المادة الخام '{name}' بنجاح\n(ملاحظة: هذه بيانات تجريبية)")
                dialog.destroy()
                self.refresh_materials_list(tree)

            except ValueError:
                messagebox.showerror("خطأ", "الرجاء إدخال أرقام صحيحة للكمية والسعر")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ:\n{e}")

        save_btn = ttb.Button(
            buttons_frame,
            text="💾 حفظ",
            command=save_material,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttb.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=tk.LEFT)

    def edit_material_dialog(self, parent, tree):
        """نافذة تعديل مادة خام"""
        selected_item = tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء اختيار مادة خام للتعديل")
            return

        material_data = tree.item(selected_item[0])['values']
        if not material_data or material_data[0] == "لا توجد مواد خام":
            messagebox.showwarning("تحذير", "لا يمكن تعديل هذا العنصر")
            return

        material_id = material_data[0]
        current_name = material_data[1]
        current_quantity = material_data[2]
        current_unit = material_data[3]
        current_price = material_data[4]
        current_supplier = material_data[5]

        dialog = ttb.Toplevel(parent)
        dialog.title("تعديل مادة خام")
        dialog.geometry("400x400")
        dialog.grab_set()
        dialog.transient(parent)
        dialog.focus_set()

        # إطار رئيسي
        main_frame = ttb.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # حقول الإدخال مع القيم الحالية
        ttb.Label(main_frame, text="اسم المادة:").pack(anchor=tk.W, pady=(0, 5))
        name_entry = ttb.Entry(main_frame, width=30)
        name_entry.insert(0, current_name)
        name_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الكمية:").pack(anchor=tk.W, pady=(0, 5))
        quantity_entry = ttb.Entry(main_frame, width=30)
        quantity_entry.insert(0, str(current_quantity))
        quantity_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الوحدة:").pack(anchor=tk.W, pady=(0, 5))
        unit_combo = ttb.Combobox(main_frame, values=["كيس", "كيلو", "طن", "لتر", "قطعة"], state="readonly")
        unit_combo.set(current_unit)
        unit_combo.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="السعر:").pack(anchor=tk.W, pady=(0, 5))
        price_entry = ttb.Entry(main_frame, width=30)
        price_entry.insert(0, str(current_price))
        price_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="المورد:").pack(anchor=tk.W, pady=(0, 5))
        supplier_entry = ttb.Entry(main_frame, width=30)
        supplier_entry.insert(0, current_supplier)
        supplier_entry.pack(fill=tk.X, pady=(0, 20))

        # أزرار الحفظ والإلغاء
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def update_material():
            name = name_entry.get().strip()
            quantity = quantity_entry.get().strip()
            unit = unit_combo.get()
            price = price_entry.get().strip()
            supplier = supplier_entry.get().strip()

            if not all([name, quantity, price, supplier]):
                messagebox.showerror("خطأ", "الرجاء ملء جميع الحقول")
                return

            try:
                float(quantity)
                float(price)

                messagebox.showinfo("نجح", f"تم تحديث المادة الخام '{name}' بنجاح\n(ملاحظة: هذه بيانات تجريبية)")
                dialog.destroy()
                self.refresh_materials_list(tree)

            except ValueError:
                messagebox.showerror("خطأ", "الرجاء إدخال أرقام صحيحة للكمية والسعر")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ:\n{e}")

        save_btn = ttb.Button(buttons_frame, text="حفظ التعديلات", command=update_material, bootstyle="success")
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttb.Button(buttons_frame, text="إلغاء", command=dialog.destroy, bootstyle="secondary")
        cancel_btn.pack(side=tk.LEFT)

    def delete_material(self, tree):
        """حذف مادة خام"""
        selected_item = tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء اختيار مادة خام للحذف")
            return

        material_data = tree.item(selected_item[0])['values']
        if not material_data or material_data[0] == "لا توجد مواد خام":
            messagebox.showwarning("تحذير", "لا يمكن حذف هذا العنصر")
            return

        material_name = material_data[1]

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المادة الخام '{material_name}'؟")
        if result:
            try:
                messagebox.showinfo("نجح", f"تم حذف المادة الخام '{material_name}' بنجاح\n(ملاحظة: هذه بيانات تجريبية)")
                self.refresh_materials_list(tree)
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ:\n{e}")

def main():
    """تشغيل التطبيق مباشرة للاختبار"""
    print("تشغيل التطبيق الرئيسي المبسط...")
    
    # مستخدم تجريبي
    test_user = {
        'user_id': 1,
        'username': 'test',
        'full_name': 'مستخدم تجريبي',
        'user_type': 'User'
    }
    
    app = MainApplication(test_user)
    app.mainloop()

if __name__ == "__main__":
    main()
