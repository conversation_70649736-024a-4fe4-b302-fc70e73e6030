@echo off
chcp 65001 >nul
title تشغيل برنامج مركز المخزون - الوضع الآمن

echo ========================================
echo    برنامج مركز المخزون - الوضع الآمن
echo ========================================
echo.

echo جاري فحص النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo جاري فحص الملفات المطلوبة...

REM فحص الملفات الأساسية
set "missing_files="
if not exist "main.py" set "missing_files=%missing_files% main.py"
if not exist "login.py" set "missing_files=%missing_files% login.py"
if not exist "database.py" set "missing_files=%missing_files% database.py"
if not exist "config.py" set "missing_files=%missing_files% config.py"
if not exist "ui_components.py" set "missing_files=%missing_files% ui_components.py"
if not exist "users.py" set "missing_files=%missing_files% users.py"

if not "%missing_files%"=="" (
    echo ❌ ملفات مفقودة: %missing_files%
    echo.
    echo يرجى التأكد من وجود جميع ملفات البرنامج
    pause
    exit /b 1
)

echo ✅ جميع الملفات الأساسية موجودة

echo.
echo جاري فحص المكتبات المطلوبة...

REM فحص المكتبات الأساسية
python -c "import tkinter" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ مكتبة tkinter مفقودة
    echo يرجى إعادة تثبيت Python مع تحديد "tcl/tk and IDLE"
    pause
    exit /b 1
)

echo ✅ مكتبة tkinter متوفرة

REM فحص باقي المكتبات
echo جاري فحص المكتبات الإضافية...
python -c "import pyodbc, customtkinter, PIL, ttkbootstrap, reportlab, arabic_reshaper, bidi, bcrypt" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  بعض المكتبات مفقودة، جاري تثبيتها...
    echo.
    
    echo جاري تحديث pip...
    python -m pip install --upgrade pip
    
    echo جاري تثبيت المكتبات...
    python -m pip install -r requirements.txt
    
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت بعض المكتبات
        echo جاري المحاولة مع خيارات إضافية...
        python -m pip install -r requirements.txt --user --upgrade --force-reinstall
        
        if %ERRORLEVEL% NEQ 0 (
            echo ❌ فشل في تثبيت المكتبات
            echo.
            echo يرجى تشغيل الأمر التالي يدوياً:
            echo python -m pip install -r requirements.txt
            echo.
            pause
            exit /b 1
        )
    )
    
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات متوفرة
)

echo.
echo جاري فحص قاعدة البيانات...

REM فحص وترقية قاعدة البيانات
if exist "upgrade_database.py" (
    echo جاري ترقية قاعدة البيانات...
    python upgrade_database.py --auto
    
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  تحذير: مشكلة في ترقية قاعدة البيانات
        echo سيتم المتابعة مع إنشاء قاعدة بيانات جديدة إذا لزم الأمر
    )
) else (
    echo ⚠️  ملف ترقية قاعدة البيانات غير موجود
)

echo.
echo ========================================
echo جاري تشغيل البرنامج...
echo ========================================
echo.

REM تشغيل البرنامج الرئيسي
python main.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo للحصول على تشخيص مفصل، شغل:
    echo python تشخيص_المشاكل.py
    echo.
    echo أو للحصول على مزيد من التفاصيل:
    echo python main.py
    echo.
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
)

echo.
pause
