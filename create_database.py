
import pyodbc
import os

# مسار قاعدة البيانات
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'flour_mill_inventory.accdb')

# إنشاء قاعدة بيانات Access
try:
    # إنشاء إتصال ODBC لإنشاء قاعدة البيانات
    conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};'

    # إذا كان الملف موجوداً بالفعل، لا تقم بإعادة إنشائه
    if not os.path.exists(db_path):
        # استخدام ADOX لإنشاء قاعدة البيانات
        import win32com.client
        cat = win32com.client.Dispatch('ADOX.Catalog')
        cat.Create(f'Provider=Microsoft.ACE.OLEDB.12.0;Data Source={db_path}')
        print(f"تم إنشاء قاعدة البيانات في {db_path}")

    # الإتصال بقاعدة البيانات
    conn = pyodbc.connect(conn_str)
    cursor = conn.cursor()

    # إنشاء الجداول مع التحقق من وجودها أولاً

    # إنشاء جدول المستخدمين
    try:
        cursor.execute("""
        CREATE TABLE Users (
            UserID AUTOINCREMENT PRIMARY KEY,
            Username VARCHAR(50) NOT NULL,
            Password VARCHAR(255) NOT NULL,
            FullName VARCHAR(100) NOT NULL,
            UserType VARCHAR(20) NOT NULL,
            LastLogin DATETIME,
            IsActive BIT DEFAULT 1
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول المواد الخام
    try:
        cursor.execute("""
        CREATE TABLE RawMaterials (
            MaterialID AUTOINCREMENT PRIMARY KEY,
            MaterialCode VARCHAR(20) NOT NULL,
            MaterialName VARCHAR(100) NOT NULL,
            Weight DOUBLE DEFAULT 0,
            Quantity DOUBLE DEFAULT 0,
            Unit VARCHAR(20),
            Description MEMO,
            LastUpdated DATETIME DEFAULT Now()
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول المنتجات الجاهزة
    try:
        cursor.execute("""
        CREATE TABLE FinishedProducts (
            ProductID AUTOINCREMENT PRIMARY KEY,
            ProductCode VARCHAR(20) NOT NULL,
            ProductName VARCHAR(100) NOT NULL,
            Weight DOUBLE DEFAULT 0,
            Quantity DOUBLE DEFAULT 0,
            Unit VARCHAR(20),
            Description MEMO,
            LastUpdated DATETIME DEFAULT Now()
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول الخلطات/المطاحين
    try:
        cursor.execute("""
        CREATE TABLE Mixtures (
            MixtureID AUTOINCREMENT PRIMARY KEY,
            MixtureCode VARCHAR(20) NOT NULL,
            MixtureName VARCHAR(100) NOT NULL,
            OperationalCode VARCHAR(50) NOT NULL,
            Description MEMO,
            CreationDate DATETIME DEFAULT Now()
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول مكونات الخلطة (العلاقة بين المواد الخام والخلطات)
    try:
        cursor.execute("""
        CREATE TABLE MixtureComponents (
            ComponentID AUTOINCREMENT PRIMARY KEY,
            MixtureID INTEGER NOT NULL,
            MaterialID INTEGER NOT NULL,
            Quantity DOUBLE NOT NULL
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول المنتجات النهائية من الخلطات (العلاقة بين المنتجات والخلطات)
    try:
        cursor.execute("""
        CREATE TABLE ProductMixtures (
            ID AUTOINCREMENT PRIMARY KEY,
            ProductID INTEGER NOT NULL,
            MixtureID INTEGER NOT NULL
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول الفئات للتطبيق الجديد
    try:
        cursor.execute("""
        CREATE TABLE Categories (
            CategoryID AUTOINCREMENT PRIMARY KEY,
            CategoryName VARCHAR(100) NOT NULL,
            Description MEMO
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول الموردين للتطبيق الجديد
    try:
        cursor.execute("""
        CREATE TABLE Suppliers (
            SupplierID AUTOINCREMENT PRIMARY KEY,
            SupplierName VARCHAR(100) NOT NULL,
            ContactPerson VARCHAR(100),
            Phone VARCHAR(20),
            Email VARCHAR(100),
            Address MEMO
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول المنتجات للتطبيق الجديد
    try:
        cursor.execute("""
        CREATE TABLE Products (
            ProductID AUTOINCREMENT PRIMARY KEY,
            ProductName VARCHAR(100) NOT NULL,
            CategoryID INTEGER,
            SupplierID INTEGER,
            UnitPrice CURRENCY,
            StockQuantity DOUBLE DEFAULT 0,
            MinimumStock DOUBLE DEFAULT 0,
            Description MEMO,
            CreatedDate DATETIME DEFAULT Now()
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إنشاء جدول معاملات المخزون للتطبيق الجديد
    try:
        cursor.execute("""
        CREATE TABLE StockTransactions (
            TransactionID AUTOINCREMENT PRIMARY KEY,
            ProductID INTEGER NOT NULL,
            TransactionType VARCHAR(20) NOT NULL,
            Quantity DOUBLE NOT NULL,
            UnitPrice CURRENCY,
            TotalAmount CURRENCY,
            TransactionDate DATETIME DEFAULT Now(),
            UserID INTEGER,
            Notes MEMO
        )
        """)
    except:
        pass  # الجدول موجود بالفعل

    # إضافة مستخدم افتراضي (admin)
    try:
        cursor.execute("""
        INSERT INTO Users (Username, Password, FullName, UserType)
        VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');
        """)
    except:
        # تجاهل الخطأ إذا كان المستخدم موجوداً بالفعل
        pass

    # إضافة بيانات تجريبية للفئات
    try:
        cursor.execute("""
        INSERT INTO Categories (CategoryName, Description)
        VALUES ('دقيق', 'أنواع الدقيق المختلفة');
        """)
        cursor.execute("""
        INSERT INTO Categories (CategoryName, Description)
        VALUES ('نخالة', 'منتجات النخالة');
        """)
        cursor.execute("""
        INSERT INTO Categories (CategoryName, Description)
        VALUES ('علف', 'أعلاف الحيوانات');
        """)
    except:
        pass  # البيانات موجودة بالفعل

    # إضافة بيانات تجريبية للموردين
    try:
        cursor.execute("""
        INSERT INTO Suppliers (SupplierName, ContactPerson, Phone)
        VALUES ('مورد القمح الأول', 'أحمد محمد', '01234567890');
        """)
        cursor.execute("""
        INSERT INTO Suppliers (SupplierName, ContactPerson, Phone)
        VALUES ('مورد الذرة', 'محمد أحمد', '01987654321');
        """)
    except:
        pass  # البيانات موجودة بالفعل

    # حفظ التغييرات
    conn.commit()
    conn.close()

    print("تم إنشاء قاعدة البيانات وجداولها بنجاح!")

except Exception as e:
    print(f"حدث خطأ أثناء إنشاء قاعدة البيانات: {e}")
