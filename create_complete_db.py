import sqlite3
import os

def create_database():
    # إنشاء قاعدة بيانات SQLite
    conn = sqlite3.connect("flour_mill_inventory.db")
    cursor = conn.cursor()

    # إنشاء جدول المستخدمين
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS Users (
        UserID INTEGER PRIMARY KEY AUTOINCREMENT,
        Username TEXT NOT NULL UNIQUE,
        Password TEXT NOT NULL,
        FullName TEXT NOT NULL,
        UserType TEXT NOT NULL,
        LastLogin DATETIME,
        IsActive INTEGER DEFAULT 1
    )
    """)

    # إضافة مستخدمين افتراضيين
    users = [
        ('test', '123', 'مستخدم تجريبي', 'User'),
        ('admin', 'admin', 'مدير النظام', 'Admin')
    ]
    
    for user in users:
        cursor.execute("""
        INSERT OR IGNORE INTO Users (Username, Password, FullName, UserType)
        VALUES (?, ?, ?, ?)
        """, user)

    # إنشاء جداول أخرى
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS RawMaterials (
        MaterialID INTEGER PRIMARY KEY AUTOINCREMENT,
        MaterialCode TEXT UNIQUE,
        MaterialName TEXT NOT NULL,
        Weight REAL DEFAULT 0,
        Quantity REAL DEFAULT 0,
        Unit TEXT DEFAULT '',
        Description TEXT DEFAULT '',
        LastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS FinishedProducts (
        ProductID INTEGER PRIMARY KEY AUTOINCREMENT,
        ProductCode TEXT UNIQUE,
        ProductName TEXT NOT NULL,
        Quantity REAL DEFAULT 0,
        Unit TEXT DEFAULT '',
        Description TEXT DEFAULT '',
        LastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS Mixtures (
        MixtureID INTEGER PRIMARY KEY AUTOINCREMENT,
        MixtureCode TEXT UNIQUE,
        MixtureName TEXT NOT NULL,
        TotalWeight REAL DEFAULT 0,
        Description TEXT DEFAULT '',
        LastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """)

    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات الكاملة بنجاح!")

if __name__ == "__main__":
    create_database()