
import tkinter as tk
from tkinter import messagebox
import sqlite3
import os
import sys

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.db_path = os.path.join(current_dir, 'flour_mill_inventory.db')

    def connect(self):
        try:
            self.connection = sqlite3.connect(self.db_path)
            return True
        except Exception as e:
            print(f'خطأ في الاتصال: {e}')
            return False

    def authenticate_user(self, username, password):
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT UserID, FullName, UserType FROM Users WHERE Username = ? AND Password = ?", 
                          (username, password))
            result = cursor.fetchone()
            
            if result:
                return {
                    'user_id': result[0],
                    'full_name': result[1],
                    'user_type': result[2],
                    'username': username
                }
            return None
        except Exception as e:
            print(f"خطأ في المصادقة: {e}")
            return None

def main():
    try:
        from login import LoginScreen
        app = LoginScreen()
        app.mainloop()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ: {e}")

if __name__ == "__main__":
    main()
