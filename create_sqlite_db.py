import sqlite3
import os

# إنشاء قاعدة بيانات SQLite
db_path = "flour_mill_inventory.db"
conn = sqlite3.connect(db_path)

# إنشاء جدول المستخدمين
conn.execute("""
CREATE TABLE IF NOT EXISTS Users (
    UserID INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL,
    Password TEXT NOT NULL,
    FullName TEXT NOT NULL,
    UserType TEXT NOT NULL
)
""")

# إضافة مستخدم افتراضي
conn.execute("""
INSERT OR IGNORE INTO Users (Username, Password, FullName, UserType)
VALUES ('test', '123', 'مستخدم تجريبي', 'User')
""")

conn.commit()
conn.close()
print("تم إنشاء قاعدة البيانات بنجاح!")