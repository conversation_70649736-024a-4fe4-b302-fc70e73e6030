import sqlite3
import os

# التحقق من وجود قاعدة البيانات
db_path = "flour_mill_inventory.db"
if os.path.exists(db_path):
    print("✅ قاعدة البيانات موجودة")
    
    # التحقق من المستخدمين
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT * FROM Users")
        users = cursor.fetchall()
        print(f"✅ عدد المستخدمين: {len(users)}")
        for user in users:
            print(f"   - {user[1]} ({user[3]})")
    except Exception as e:
        print(f"❌ خطأ في قراءة المستخدمين: {e}")
    
    conn.close()
else:
    print("❌ قاعدة البيانات غير موجودة")

