🔧 تعليمات اختبار النوافذ المحدثة
================================

✅ التحديثات التي تم إجراؤها:
=============================

1️⃣ زيادة حجم النوافذ:
   - نافذة إضافة المستخدم: 500x550 بكسل
   - نافذة إضافة المادة الخام: 550x600 بكسل

2️⃣ إضافة شريط التمرير:
   - تم إضافة Canvas مع Scrollbar
   - يمكن التمرير إذا كان المحتوى أكبر من النافذة

3️⃣ تحسين التصميم:
   - إضافة إطارات منظمة (LabelFrame)
   - تحسين الخطوط والألوان
   - توسيط الأزرار

4️⃣ وضع النوافذ في وسط الشاشة:
   - النوافذ تظهر في منتصف الشاشة تلقائياً

🧪 خطوات الاختبار:
==================

1️⃣ تشغيل البرنامج:
   python main.py

2️⃣ تسجيل الدخول:
   - المستخدم: test
   - كلمة المرور: 123

3️⃣ اختبار إضافة مستخدم:
   - انقر على "إدارة المستخدمين"
   - انقر على "إضافة مستخدم"
   - يجب أن ترى:
     ✅ عنوان "📝 إضافة مستخدم جديد"
     ✅ إطار "👤 بيانات المستخدم"
     ✅ إطار "🔧 العمليات"
     ✅ زر "💾 حفظ المستخدم"
     ✅ زر "❌ إلغاء"

4️⃣ اختبار إضافة مادة خام:
   - انقر على "إدارة المواد الخام"
   - انقر على "إضافة مادة خام"
   - يجب أن ترى:
     ✅ عنوان "📦 إضافة مادة خام جديدة"
     ✅ جميع حقول الإدخال
     ✅ أزرار الحفظ والإلغاء في الأسفل

🎯 النتيجة المتوقعة:
==================

✅ النوافذ أكبر وأوضح
✅ الأزرار ظاهرة في الأسفل
✅ التصميم منظم ومرتب
✅ يمكن التمرير إذا لزم الأمر
✅ النوافذ في وسط الشاشة

📞 إذا لم تظهر الأزرار:
========================

❌ تأكد من:
   - حجم الشاشة مناسب
   - عدم وجود برامج أخرى تحجب النافذة
   - النقر على النافذة لتفعيلها

✅ جرب:
   - تحريك النافذة
   - النقر داخل النافذة
   - استخدام Alt+Tab للتنقل بين النوافذ

🎉 استمتع بالاختبار!
===================
