#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from database import DatabaseManager, UserManager, SecurityManager

def check_existing_users():
    """فحص المستخدمين الموجودين في قاعدة البيانات"""
    print("=" * 50)
    print("فحص المستخدمين الموجودين...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # الاتصال بقاعدة البيانات
        if not db.connect():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        # استعلام للحصول على جميع المستخدمين
        query = "SELECT UserID, Username, FullName, UserType FROM Users"
        users = db.execute_query(query)
        
        if users:
            print(f"✅ تم العثور على {len(users)} مستخدم:")
            print("-" * 50)
            for user in users:
                print(f"المعرف: {user[0] if len(user) > 0 else 'غير محدد'}")
                print(f"اسم المستخدم: {user[1] if len(user) > 1 else 'غير محدد'}")
                print(f"الاسم الكامل: {user[2] if len(user) > 2 else 'غير محدد'}")
                print(f"الدور: {user[3] if len(user) > 3 else 'غير محدد'}")
                print("-" * 30)
        else:
            print("⚠️ لا يوجد مستخدمين في قاعدة البيانات")
        
        db.disconnect()
        return len(users) if users else 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص المستخدمين: {e}")
        return False

def create_default_user():
    """إنشاء مستخدم افتراضي للنظام"""
    print("=" * 50)
    print("إنشاء مستخدم افتراضي...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        user_manager = UserManager(db)
        
        # الاتصال بقاعدة البيانات
        if not db.connect():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        # بيانات المستخدم الافتراضي
        default_users = [
            {
                'username': 'admin',
                'password': '123456',
                'full_name': 'مدير النظام',
                'role': 'Admin',
                'email': '<EMAIL>'
            },
            {
                'username': 'user',
                'password': '123456',
                'full_name': 'مستخدم عادي',
                'role': 'User',
                'email': '<EMAIL>'
            }
        ]
        
        created_count = 0
        
        for user_data in default_users:
            # التحقق من وجود المستخدم
            existing_user = user_manager.get_user_by_username(user_data['username'])
            
            if existing_user:
                print(f"⚠️ المستخدم '{user_data['username']}' موجود بالفعل")
                continue
            
            # تشفير كلمة المرور
            hashed_password = SecurityManager.hash_password(user_data['password'])
            
            # إنشاء المستخدم
            success = user_manager.create_user(
                username=user_data['username'],
                password=hashed_password,
                full_name=user_data['full_name'],
                role=user_data['role']
            )
            
            if success:
                print(f"✅ تم إنشاء المستخدم '{user_data['username']}' بنجاح")
                print(f"   - الاسم الكامل: {user_data['full_name']}")
                print(f"   - الدور: {user_data['role']}")
                print(f"   - كلمة المرور: {user_data['password']}")
                created_count += 1
            else:
                print(f"❌ فشل في إنشاء المستخدم '{user_data['username']}'")
        
        db.disconnect()
        
        if created_count > 0:
            print(f"\n🎉 تم إنشاء {created_count} مستخدم جديد بنجاح!")
            print("\nيمكنك الآن تسجيل الدخول باستخدام:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: 123456")
            print("\nأو:")
            print("اسم المستخدم: user")
            print("كلمة المرور: 123456")
        
        return created_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم الافتراضي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إنشاء المستخدم الافتراضي")
    print("=" * 50)
    
    # فحص المستخدمين الموجودين
    user_count = check_existing_users()
    
    if user_count is False:
        print("❌ فشل في فحص قاعدة البيانات")
        return
    
    # إنشاء مستخدمين افتراضيين
    if user_count == 0:
        print("\n📝 لا يوجد مستخدمين، سيتم إنشاء مستخدمين افتراضيين...")
        create_default_user()
    else:
        print(f"\n✅ يوجد {user_count} مستخدم في النظام")
        
        # السؤال عن إنشاء مستخدمين إضافيين
        response = input("\nهل تريد إنشاء مستخدمين افتراضيين إضافيين؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم', 'ن']:
            create_default_user()
        else:
            print("تم إلغاء العملية.")

if __name__ == "__main__":
    main()
