#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح توافق قاعدة البيانات - بدون تأثير على البيانات الموجودة
"""

import sqlite3
import os
from datetime import datetime

def ensure_database_compatibility():
    """التأكد من توافق قاعدة البيانات مع الكود الحالي"""
    try:
        # فحص وجود قاعدة البيانات
        db_exists = os.path.exists("flour_mill_inventory.db")
        
        conn = sqlite3.connect("flour_mill_inventory.db")
        cursor = conn.cursor()
        
        # إنشاء الجدول إذا لم يكن موجود<|im_start|> (بدون تأثير على الموجود)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Users (
                UserID INTEGER PRIMARY KEY AUTOINCREMENT,
                Username TEXT UNIQUE NOT NULL,
                Password TEXT NOT NULL,
                FullName TEXT NOT NULL,
                UserType TEXT DEFAULT 'User',
                CreatedDate TEXT,
                IsActive BOOLEAN DEFAULT 1
            )
        """)
        
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(Users)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة الأعمدة المفقودة فقط (بدون تأثير على الموجود)
        if 'CreatedDate' not in existing_columns:
            cursor.execute("ALTER TABLE Users ADD COLUMN CreatedDate TEXT")
            # تحديث السجلات الموجودة بتاريخ افتراضي
            cursor.execute("""
                UPDATE Users 
                SET CreatedDate = ? 
                WHERE CreatedDate IS NULL
            """, (datetime.now().strftime('%Y-%m-%d %H:%M:%S'),))
            print("✅ تم إضافة عمود CreatedDate")
        
        if 'IsActive' not in existing_columns:
            cursor.execute("ALTER TABLE Users ADD COLUMN IsActive BOOLEAN DEFAULT 1")
            # تحديث السجلات الموجودة بحالة نشطة افتراضية
            cursor.execute("UPDATE Users SET IsActive = 1 WHERE IsActive IS NULL")
            print("✅ تم إضافة عمود IsActive")
        
        # إضافة بيانات تجريبية فقط إذا كانت قاعدة البيانات فارغة
        cursor.execute("SELECT COUNT(*) FROM Users")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            users_data = [
                ("test", "123", "مستخدم تجريبي", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("admin", "admin", "مدير النظام", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("user1", "123456", "أحمد محمد", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("user2", "123456", "فاطمة علي", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1)
            ]
            
            cursor.executemany("""
                INSERT INTO Users (Username, Password, FullName, UserType, CreatedDate, IsActive)
                VALUES (?, ?, ?, ?, ?, ?)
            """, users_data)
            print(f"✅ تم إضافة {len(users_data)} مستخدمين تجريبيين")
        
        conn.commit()
        conn.close()
        
        print("✅ تم التأكد من توافق قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    ensure_database_compatibility()