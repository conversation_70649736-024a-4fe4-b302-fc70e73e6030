#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
import ttkbootstrap as ttb
from tkinter import messagebox

def test_simple_dialog():
    """اختبار نافذة بسيطة مع أزرار الحفظ"""
    root = ttb.Window(themename="cosmo")
    root.title("اختبار النافذة البسيطة")
    root.geometry("300x200")
    
    def open_dialog():
        dialog = ttb.Toplevel(root)
        dialog.title("إضافة مستخدم جديد")
        dialog.geometry("450x500")
        dialog.grab_set()
        dialog.transient(root)
        dialog.focus_set()
        dialog.resizable(False, False)

        # إطار رئيسي بسيط
        main_frame = ttb.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان
        title_label = ttb.Label(
            main_frame,
            text="إضافة مستخدم جديد",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # حقول الإدخال
        ttb.Label(main_frame, text="اسم المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        username_entry = ttb.Entry(main_frame, width=30)
        username_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="كلمة المرور:").pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttb.Entry(main_frame, width=30, show="*")
        password_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="الاسم الكامل:").pack(anchor=tk.W, pady=(0, 5))
        fullname_entry = ttb.Entry(main_frame, width=30)
        fullname_entry.pack(fill=tk.X, pady=(0, 10))

        ttb.Label(main_frame, text="نوع المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        usertype_combo = ttb.Combobox(main_frame, values=["User", "Admin"], state="readonly")
        usertype_combo.set("User")
        usertype_combo.pack(fill=tk.X, pady=(0, 20))

        # أزرار الحفظ والإلغاء
        buttons_frame = ttb.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        def save_user():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            fullname = fullname_entry.get().strip()
            usertype = usertype_combo.get()

            if not all([username, password, fullname]):
                messagebox.showerror("خطأ", "الرجاء ملء جميع الحقول")
                return
            
            messagebox.showinfo("نجح", f"تم حفظ المستخدم: {fullname}")
            dialog.destroy()

        save_btn = ttb.Button(
            buttons_frame,
            text="حفظ",
            command=save_user,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttb.Button(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=tk.LEFT)
        
        # تركيز على أول حقل
        username_entry.focus_set()
    
    # زر لفتح النافذة
    open_btn = ttb.Button(
        root,
        text="فتح نافذة الإضافة",
        command=open_dialog,
        bootstyle="primary"
    )
    open_btn.pack(expand=True)
    
    root.mainloop()

if __name__ == "__main__":
    test_simple_dialog()
