#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime

class InventoryApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مركز المخزون - نظام إدارة مخزون المطحنة")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        self.init_database()
        
        # إنشاء القائمة الرئيسية
        self.create_main_menu()
        
        # إنشاء الواجهة
        self.create_widgets()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول - محسن مع الحفاظ على التعديلات السابقة"""
        try:
            # استيراد إصلاح التوافق (إضافة جديدة)
            try:
                from database_compatibility_fix import ensure_database_compatibility
                if ensure_database_compatibility():
                    return  # إذا تم الإصلاح بنجاح، لا حاجة للمتابعة
            except ImportError:
                pass  # إذا لم يوجد الملف، استمر بالطريقة العادية
            
            # الكود الأصلي محفوظ كما هو
            conn = sqlite3.connect("flour_mill_inventory.db")
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS Users (
                    UserID INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT UNIQUE NOT NULL,
                    Password TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    UserType TEXT DEFAULT 'User',
                    CreatedDate TEXT,
                    IsActive BOOLEAN DEFAULT 1
                )
            """)
            
            # التحقق من وجود بيانات
            cursor.execute("SELECT COUNT(*) FROM Users")
            count = cursor.fetchone()[0]
            
            # إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
            if count == 0:
                users_data = [
                    ("test", "123", "مستخدم تجريبي", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                    ("admin", "admin", "مدير النظام", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                    ("user1", "123456", "أحمد محمد", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                    ("user2", "123456", "فاطمة علي", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1)
                ]
                
                cursor.executemany("""
                    INSERT INTO Users (Username, Password, FullName, UserType, CreatedDate, IsActive)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, users_data)
                
                print(f"✅ تم إضافة {len(users_data)} مستخدمين تجريبيين")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
    
    def create_widgets(self):
        """إنشاء واجهة المستخدم"""
        # العنوان الرئيسي
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="👥 إدارة المستخدمين", 
                font=("Arial", 20, "bold"), bg='#2c3e50', fg='white').pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = tk.Frame(self.root, bg='#ecf0f1', height=60)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)
        toolbar_frame.pack_propagate(False)
        
        # أزرار شريط الأدوات
        tk.Button(toolbar_frame, text="➕ إضافة مستخدم", command=self.add_user_dialog,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 width=15, height=2).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(toolbar_frame, text="✏️ تعديل مستخدم", command=self.edit_selected_user,
                 bg='#f39c12', fg='white', font=("Arial", 12, "bold"),
                 width=15, height=2).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(toolbar_frame, text="🗑️ حذف مستخدم", command=self.delete_selected_user,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 width=15, height=2).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(toolbar_frame, text="🔄 تحديث", command=self.load_users_data,
                 bg='#3498db', fg='white', font=("Arial", 12, "bold"),
                 width=12, height=2).pack(side=tk.LEFT, padx=5, pady=10)
        
        tk.Button(toolbar_frame, text="📊 إضافة بيانات تجريبية", command=self.add_sample_data_and_refresh,
                 bg='#9b59b6', fg='white', font=("Arial", 12, "bold"),
                 width=18, height=2).pack(side=tk.LEFT, padx=5, pady=10)
        
        # جدول المستخدمين
        table_frame = tk.Frame(self.root, bg='#f0f0f0')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء Treeview
        columns = ("ID", "اسم المستخدم", "الاسم الكامل", "النوع", "تاريخ الإنشاء", "الحالة")
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحميل البيانات
        self.load_users_data()
    
    def load_users_data(self):
        """تحميل بيانات المستخدمين - محسن مع الحفاظ على التعديلات السابقة"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            conn = sqlite3.connect("flour_mill_inventory.db")
            cursor = conn.cursor()
            
            # التحقق من الأعمدة الموجودة أولاً (إضافة جديدة للحماية)
            cursor.execute("PRAGMA table_info(Users)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # استعلام مرن حسب الأعمدة المتاحة (تحسين جديد)
            if 'CreatedDate' in columns and 'IsActive' in columns:
                query = """
                    SELECT UserID, Username, FullName, UserType, 
                           COALESCE(CreatedDate, 'غير محدد'), COALESCE(IsActive, 1)
                    FROM Users ORDER BY UserID
                """
            elif 'CreatedDate' in columns:
                query = """
                    SELECT UserID, Username, FullName, UserType, 
                           COALESCE(CreatedDate, 'غير محدد'), 1
                    FROM Users ORDER BY UserID
                """
            else:
                query = """
                    SELECT UserID, Username, FullName, UserType, 
                           'غير محدد', 1
                    FROM Users ORDER BY UserID
                """
            
            cursor.execute(query)
            users = cursor.fetchall()
            conn.close()
            
            print(f"📊 تم العثور على {len(users)} مستخدمين في قاعدة البيانات")
            
            # إضافة البيانات للجدول (الكود الأصلي محفوظ)
            for user in users:
                status = "نشط" if user[5] else "معطل"
                self.users_tree.insert("", "end", values=(
                    user[0], user[1], user[2], user[3], user[4], status
                ))
                print(f"✅ تم إضافة المستخدم: {user[1]} - {user[2]}")
            
            # إذا لم توجد بيانات، أضف بيانات تجريبية (الكود الأصلي محفوظ)
            if len(users) == 0:
                print("⚠️ لا توجد بيانات، سيتم إضافة بيانات تجريبية...")
                self.add_sample_data()
                # إعادة تحميل البيانات
                self.load_users_data()
        
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المستخدمين:\n{str(e)}")

    def add_sample_data(self):
        """إضافة بيانات تجريبية"""
        try:
            conn = sqlite3.connect("flour_mill_inventory.db")
            cursor = conn.cursor()
            
            users_data = [
                ("test", "123", "مستخدم تجريبي", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("admin", "admin", "مدير النظام", "Admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("user1", "123456", "أحمد محمد", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("user2", "123456", "فاطمة علي", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 1),
                ("user3", "123456", "محمد حسن", "User", datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 0)
            ]
            
            for user_data in users_data:
                try:
                    cursor.execute("""
                        INSERT INTO Users (Username, Password, FullName, UserType, CreatedDate, IsActive)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, user_data)
                except sqlite3.IntegrityError:
                    # المستخدم موجود بالفعل
                    pass
            
            conn.commit()
            conn.close()
            print("✅ تم إضافة البيانات التجريبية")
        
        except Exception as e:
            print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
    
    def add_sample_data_and_refresh(self):
        """إضافة بيانات تجريبية وتحديث الجدول"""
        self.add_sample_data()
        self.load_users_data()
        messagebox.showinfo("تم", "تم إضافة البيانات التجريبية وتحديث الجدول!")
    
    def add_user_dialog(self):
        """نافذة إضافة مستخدم جديد"""
        add_window = tk.Toplevel(self.root)
        add_window.title("إضافة مستخدم جديد")
        add_window.geometry("500x650")
        add_window.configure(bg='#f0f0f0')
        add_window.resizable(False, False)
        add_window.grab_set()
        add_window.transient(self.root)
        
        # وضع النافذة في المنتصف
        add_window.update_idletasks()
        x = (add_window.winfo_screenwidth() // 2) - (250)
        y = (add_window.winfo_screenheight() // 2) - (325)
        add_window.geometry(f"500x650+{x}+{y}")
        
        # الشريط العلوي الأخضر مع العنوان
        header_frame = tk.Frame(add_window, bg='#27ae60', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="➕ إضافة مستخدم جديد", 
                font=("Arial", 14, "bold"), bg='#27ae60', fg='white').pack(pady=15)
        
        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(add_window, bg='#f0f0f0')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        # متغيرات النموذج
        username_var = tk.StringVar()
        password_var = tk.StringVar()
        confirm_password_var = tk.StringVar()
        fullname_var = tk.StringVar()
        usertype_var = tk.StringVar(value="User")
        is_active_var = tk.BooleanVar(value=True)
        
        # اسم المستخدم
        user_label = tk.Label(content_frame, text=": اسم المستخدم 👤", 
                             font=("Arial", 11, "bold"), bg='#f0f0f0')
        user_label.pack(anchor='e', pady=(10, 5))
        
        username_entry = tk.Entry(content_frame, textvariable=username_var, 
                                 font=("Arial", 11), width=40, justify='right')
        username_entry.pack(pady=(0, 15), ipady=3)
        
        # كلمة المرور
        pass_label = tk.Label(content_frame, text=": كلمة المرور 🔒", 
                             font=("Arial", 11, "bold"), bg='#f0f0f0')
        pass_label.pack(anchor='e', pady=(0, 5))
        
        password_entry = tk.Entry(content_frame, textvariable=password_var, 
                                 font=("Arial", 11), width=40, show="*", justify='right')
        password_entry.pack(pady=(0, 15), ipady=3)
        
        # تأكيد كلمة المرور
        confirm_label = tk.Label(content_frame, text=": تأكيد كلمة المرور 🔒", 
                                font=("Arial", 11, "bold"), bg='#f0f0f0')
        confirm_label.pack(anchor='e', pady=(0, 5))
        
        confirm_entry = tk.Entry(content_frame, textvariable=confirm_password_var, 
                                font=("Arial", 11), width=40, show="*", justify='right')
        confirm_entry.pack(pady=(0, 15), ipady=3)
        
        # الاسم الكامل
        name_label = tk.Label(content_frame, text=": الاسم الكامل 📝", 
                             font=("Arial", 11, "bold"), bg='#f0f0f0')
        name_label.pack(anchor='e', pady=(0, 5))
        
        fullname_entry = tk.Entry(content_frame, textvariable=fullname_var, 
                                 font=("Arial", 11), width=40, justify='right')
        fullname_entry.pack(pady=(0, 20), ipady=3)
        
        # نوع المستخدم
        type_label = tk.Label(content_frame, text=": نوع المستخدم ⚙️", 
                             font=("Arial", 11, "bold"), bg='#f0f0f0')
        type_label.pack(anchor='e', pady=(0, 10))
        
        radio_frame = tk.Frame(content_frame, bg='#f0f0f0')
        radio_frame.pack(anchor='e', pady=(0, 20))
        
        tk.Radiobutton(radio_frame, text="مستخدم عادي 👤", variable=usertype_var, 
                      value="User", font=("Arial", 10), bg='#f0f0f0').pack(anchor='e')
        tk.Radiobutton(radio_frame, text="مدير 👨‍💼", variable=usertype_var, 
                      value="Admin", font=("Arial", 10), bg='#f0f0f0').pack(anchor='e')
        
        # حالة المستخدم
        status_label = tk.Label(content_frame, text=": حالة المستخدم 🟢", 
                               font=("Arial", 11, "bold"), bg='#f0f0f0')
        status_label.pack(anchor='e', pady=(0, 10))
        
        tk.Checkbutton(content_frame, text="مستخدم نشط ✓", variable=is_active_var, 
                      font=("Arial", 10), bg='#f0f0f0').pack(anchor='e', pady=(0, 20))
        
        # إطار الملاحظات
        notes_frame = tk.LabelFrame(content_frame, text="ملاحظات مهمة ⚠️", 
                                   font=("Arial", 10, "bold"), bg='#f0f0f0')
        notes_frame.pack(fill=tk.X, pady=(10, 30))
        
        notes_text = """• اسم المستخدم يجب أن يكون فريداً
• كلمة المرور يجب أن تكون 6 أحرف على الأقل
• تأكد من صحة كلمة المرور قبل الحفظ
• يمكن تعطيل المستخدم لاحق<|im_start|>"""
        
        tk.Label(notes_frame, text=notes_text, font=("Arial", 9), 
                bg='#f0f0f0', justify=tk.RIGHT).pack(padx=10, pady=8)
        
        # إطار الأزرار في الأسفل
        buttons_frame = tk.Frame(add_window, bg='#f0f0f0')
        buttons_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=40, pady=15)
        
        def save_user():
            username = username_var.get().strip()
            password = password_var.get()
            confirm_password = confirm_password_var.get()
            fullname = fullname_var.get().strip()
            usertype = usertype_var.get()
            is_active = is_active_var.get()
            
            if not username:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
                return
            if len(password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return
            if password != confirm_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return
            if not fullname:
                messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
                return
            
            try:
                conn = sqlite3.connect("flour_mill_inventory.db")
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM Users WHERE Username = ?", (username,))
                if cursor.fetchone()[0] > 0:
                    messagebox.showerror("خطأ", f"اسم المستخدم '{username}' موجود بالفعل")
                    conn.close()
                    return
                
                cursor.execute("""
                    INSERT INTO Users (Username, Password, FullName, UserType, CreatedDate, IsActive)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (username, password, fullname, usertype, 
                     datetime.now().strftime('%Y-%m-%d %H:%M:%S'), is_active))
                
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", f"تم إضافة المستخدم '{username}' بنجاح!")
                add_window.destroy()
                self.load_users_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المستخدم:\n{str(e)}")
        
        def cancel():
            add_window.destroy()
        
        # الأزرار
        tk.Button(buttons_frame, text="💾 حفظ المستخدم", command=save_user,
                 bg='#27ae60', fg='white', font=("Arial", 11, "bold"),
                 width=15, height=2).pack(side=tk.RIGHT, padx=(10, 0))
        
        tk.Button(buttons_frame, text="❌ إلغاء", command=cancel,
                 bg='#e74c3c', fg='white', font=("Arial", 11, "bold"),
                 width=12, height=2).pack(side=tk.RIGHT)
        
        username_entry.focus()
    
    def edit_selected_user(self):
        """تعديل المستخدم المحدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        item = self.users_tree.item(selected[0])
        user_data = item['values']
        user_id = user_data[0]
        
        # نافذة التعديل
        edit_window = tk.Toplevel(self.root)
        edit_window.title("تعديل مستخدم")
        edit_window.geometry("500x650")
        edit_window.configure(bg='#f0f0f0')
        edit_window.resizable(False, False)
        edit_window.grab_set()
        edit_window.transient(self.root)
        
        # وضع النافذة في المنتصف
        edit_window.update_idletasks()
        x = (edit_window.winfo_screenwidth() // 2) - (250)
        y = (edit_window.winfo_screenheight() // 2) - (325)
        edit_window.geometry(f"500x650+{x}+{y}")
        
        # الشريط العلوي البرتقالي
        header_frame = tk.Frame(edit_window, bg='#f39c12', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="✏️ تعديل مستخدم", 
                font=("Arial", 14, "bold"), bg='#f39c12', fg='white').pack(pady=15)
        
        # إطار المحتوى
        content_frame = tk.Frame(edit_window, bg='#f0f0f0')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        # جلب بيانات المستخدم الحالية
        try:
            conn = sqlite3.connect("flour_mill_inventory.db")
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM Users WHERE UserID = ?", (user_id,))
            current_user = cursor.fetchone()
            conn.close()
            
            if not current_user:
                messagebox.showerror("خطأ", "لم يتم العثور على المستخدم")
                edit_window.destroy()
                return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في جلب بيانات المستخدم: {e}")
            edit_window.destroy()
            return
        
        # متغيرات النموذج مع البيانات الحالية
        username_var = tk.StringVar(value=current_user[1])
        password_var = tk.StringVar(value=current_user[2])
        confirm_password_var = tk.StringVar(value=current_user[2])
        fullname_var = tk.StringVar(value=current_user[3])
        usertype_var = tk.StringVar(value=current_user[4])
        is_active_var = tk.BooleanVar(value=bool(current_user[6]))
        
        # اسم المستخدم
        tk.Label(content_frame, text=": اسم المستخدم 👤", 
                 font=("Arial", 11, "bold"), bg='#f0f0f0').pack(anchor='e', pady=(10, 5))
        username_entry = tk.Entry(content_frame, textvariable=username_var, 
                                 font=("Arial", 11), width=40, justify='right')
        username_entry.pack(pady=(0, 15), ipady=3)
        
        # كلمة المرور
        tk.Label(content_frame, text=": كلمة المرور 🔒", 
                 font=("Arial", 11, "bold"), bg='#f0f0f0').pack(anchor='e', pady=(0, 5))
        password_entry = tk.Entry(content_frame, textvariable=password_var, 
                                 font=("Arial", 11), width=40, show="*", justify='right')
        password_entry.pack(pady=(0, 15), ipady=3)
        
        # تأكيد كلمة المرور
        tk.Label(content_frame, text=": تأكيد كلمة المرور 🔒", 
                 font=("Arial", 11, "bold"), bg='#f0f0f0').pack(anchor='e', pady=(0, 5))
        confirm_entry = tk.Entry(content_frame, textvariable=confirm_password_var, 
                                font=("Arial", 11), width=40, show="*", justify='right')
        confirm_entry.pack(pady=(0, 15), ipady=3)
        
        # الاسم الكامل
        tk.Label(content_frame, text=": الاسم الكامل 📝", 
                 font=("Arial", 11, "bold"), bg='#f0f0f0').pack(anchor='e', pady=(0, 5))
        fullname_entry = tk.Entry(content_frame, textvariable=fullname_var, 
                                 font=("Arial", 11), width=40, justify='right')
        fullname_entry.pack(pady=(0, 20), ipady=3)
        
        # نوع المستخدم
        tk.Label(content_frame, text=": نوع المستخدم ⚙️", 
                 font=("Arial", 11, "bold"), bg='#f0f0f0').pack(anchor='e', pady=(0, 10))
        
        radio_frame = tk.Frame(content_frame, bg='#f0f0f0')
        radio_frame.pack(anchor='e', pady=(0, 20))
        
        tk.Radiobutton(radio_frame, text="مستخدم عادي 👤", variable=usertype_var, 
                      value="User", font=("Arial", 10), bg='#f0f0f0').pack(anchor='e')
        tk.Radiobutton(radio_frame, text="مدير 👨‍💼", variable=usertype_var, 
                      value="Admin", font=("Arial", 10), bg='#f0f0f0').pack(anchor='e')
        
        # حالة المستخدم
        tk.Label(content_frame, text=": حالة المستخدم 🟢", 
                 font=("Arial", 11, "bold"), bg='#f0f0f0').pack(anchor='e', pady=(0, 10))
        tk.Checkbutton(content_frame, text="مستخدم نشط ✓", variable=is_active_var, 
                      font=("Arial", 10), bg='#f0f0f0').pack(anchor='e', pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(edit_window, bg='#f0f0f0')
        buttons_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=40, pady=15)
        
        def update_user():
            username = username_var.get().strip()
            password = password_var.get()
            confirm_password = confirm_password_var.get()
            fullname = fullname_var.get().strip()
            usertype = usertype_var.get()
            is_active = is_active_var.get()
            
            if not username:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
                return
            if len(password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return
            if password != confirm_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return
            if not fullname:
                messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
                return
            
            try:
                conn = sqlite3.connect("flour_mill_inventory.db")
                cursor = conn.cursor()
                
                # التحقق من عدم تكرار اسم المستخدم (عدا المستخدم الحالي)
                cursor.execute("SELECT COUNT(*) FROM Users WHERE Username = ? AND UserID != ?", (username, user_id))
                if cursor.fetchone()[0] > 0:
                    messagebox.showerror("خطأ", f"اسم المستخدم '{username}' موجود بالفعل")
                    conn.close()
                    return
                
                # تحديث بيانات المستخدم
                cursor.execute("""
                    UPDATE Users 
                    SET Username = ?, Password = ?, FullName = ?, UserType = ?, IsActive = ?
                    WHERE UserID = ?
                """, (username, password, fullname, usertype, is_active, user_id))
                
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", f"تم تحديث بيانات المستخدم '{username}' بنجاح!")
                edit_window.destroy()
                self.load_users_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث المستخدم:\n{str(e)}")
        
        def cancel():
            edit_window.destroy()
        
        # الأزرار
        tk.Button(buttons_frame, text="💾 حفظ التعديلات", command=update_user,
                 bg='#f39c12', fg='white', font=("Arial", 11, "bold"),
                 width=15, height=2).pack(side=tk.RIGHT, padx=(10, 0))
        
        tk.Button(buttons_frame, text="❌ إلغاء", command=cancel,
                 bg='#e74c3c', fg='white', font=("Arial", 11, "bold"),
                 width=12, height=2).pack(side=tk.RIGHT)
        
        username_entry.focus()

    def delete_selected_user(self):
        """حذف المستخدم المحدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        item = self.users_tree.item(selected[0])
        user_data = item['values']
        user_id = user_data[0]
        username = user_data[1]
        
        # التأكيد من الحذف
        if not messagebox.askyesno("تأكيد الحذف", 
                                  f"هل تريد حذف المستخدم '{username}'؟\n\n"
                                  "⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!"):
            return
        
        try:
            conn = sqlite3.connect("flour_mill_inventory.db")
            cursor = conn.cursor()
            
            # حذف المستخدم
            cursor.execute("DELETE FROM Users WHERE UserID = ?", (user_id,))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", f"تم حذف المستخدم '{username}' بنجاح!")
            
            # تحديث قائمة المستخدمين
            self.load_users_data()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المستخدم:\n{str(e)}")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

    def create_main_menu(self):
        """إنشاء القائمة الرئيسية للتطبيق"""
        # إضافة شريط القوائم
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="جديد", command=self.new_database)
        file_menu.add_command(label="فتح", command=self.open_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="ملف", menu=file_menu)
        
        # قائمة المستخدمين
        users_menu = tk.Menu(menubar, tearoff=0)
        users_menu.add_command(label="إضافة مستخدم", command=self.add_user_dialog)
        users_menu.add_command(label="تعديل مستخدم", command=self.edit_selected_user)
        users_menu.add_command(label="حذف مستخدم", command=self.delete_selected_user)
        users_menu.add_separator()
        users_menu.add_command(label="تحديث القائمة", command=self.load_users_data)
        menubar.add_cascade(label="المستخدمين", menu=users_menu)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        tools_menu.add_command(label="نسخ احتياطي", command=self.backup_database)
        tools_menu.add_command(label="استعادة", command=self.restore_database)
        tools_menu.add_separator()
        tools_menu.add_command(label="إعدادات", command=self.show_settings)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        menubar.add_cascade(label="مساعدة", menu=help_menu)

    def new_database(self):
        """إنشاء قاعدة بيانات جديدة"""
        if messagebox.askyesno("تأكيد", "هل تريد إنشاء قاعدة بيانات جديدة؟\nسيتم حذف جميع البيانات الحالية!"):
            try:
                if os.path.exists("flour_mill_inventory.db"):
                    os.remove("flour_mill_inventory.db")
                self.init_database()
                self.load_users_data()
                messagebox.showinfo("نجح", "تم إنشاء قاعدة بيانات جديدة بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء قاعدة البيانات:\n{str(e)}")

    def open_database(self):
        """فتح قاعدة بيانات موجودة"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="اختر قاعدة البيانات",
            filetypes=[("SQLite files", "*.db"), ("All files", "*.*")]
        )
        if filename:
            try:
                # نسخ الملف المختار
                import shutil
                shutil.copy2(filename, "flour_mill_inventory.db")
                self.load_users_data()
                messagebox.showinfo("نجح", "تم فتح قاعدة البيانات بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح قاعدة البيانات:\n{str(e)}")

    def backup_database(self):
        """عمل نسخة احتياطية من قاعدة البيانات"""
        from tkinter import filedialog
        import shutil
        from datetime import datetime
        
        filename = filedialog.asksaveasfilename(
            title="حفظ النسخة الاحتياطية",
            defaultextension=".db",
            initialvalue=f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db",
            filetypes=[("SQLite files", "*.db"), ("All files", "*.*")]
        )
        if filename:
            try:
                shutil.copy2("flour_mill_inventory.db", filename)
                messagebox.showinfo("نجح", f"تم حفظ النسخة الاحتياطية في:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ النسخة الاحتياطية:\n{str(e)}")

    def restore_database(self):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة قاعدة البيانات؟\nسيتم استبدال البيانات الحالية!"):
            self.open_database()

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        messagebox.showinfo("الإعدادات", "ستتوفر نافذة الإعدادات في الإصدار القادم")

    def show_help(self):
        """عرض دليل المساعدة"""
        help_text = """دليل استخدام نظام إدارة المستخدمين:

1. إضافة مستخدم جديد:
   - انقر على زر "إضافة مستخدم"
   - املأ البيانات المطلوبة
   - انقر على "حفظ المستخدم"

2. تعديل مستخدم:
   - اختر المستخدم من الجدول
   - انقر على زر "تعديل مستخدم"
   - عدل البيانات المطلوبة
   - انقر على "حفظ التعديلات"

3. حذف مستخدم:
   - اختر المستخدم من الجدول
   - انقر على زر "حذف مستخدم"
   - أكد الحذف

4. تحديث القائمة:
   - انقر على زر "تحديث" لإعادة تحميل البيانات"""
   
        messagebox.showinfo("دليل المساعدة", help_text)

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """نظام إدارة مخزون المطحنة
الإصدار 1.0

تم تطويره لإدارة المستخدمين والمواد الخام
والمنتجات الجاهزة في المطاحن

© 2024 جميع الحقوق محفوظة"""
    
        messagebox.showinfo("حول البرنامج", about_text)

if __name__ == "__main__":
    print("🚀 تشغيل تطبيق إدارة المستخدمين - الإصدار النظيف")
    print("📁 الملف: start_app_clean.py")
    app = InventoryApp()
    app.run()






