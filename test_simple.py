
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tkinter as tk
from tkinter import messagebox

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        from database import DatabaseManager, UserManager
        print("✅ تم استيراد database.py بنجاح")
        
        db = DatabaseManager()
        print("✅ تم إنشاء DatabaseManager بنجاح")
        
        user_manager = UserManager(db)
        print("✅ تم إنشاء UserManager بنجاح")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ui():
    """اختبار واجهة المستخدم البسيطة"""
    try:
        root = tk.Tk()
        root.title("اختبار واجهة المستخدم")
        root.geometry("400x300")
        
        label = tk.Label(root, text="مرحباً! هذا اختبار بسيط لواجهة المستخدم", font=("Arial", 12))
        label.pack(pady=20)
        
        def close_app():
            root.destroy()
        
        button = tk.Button(root, text="إغلاق", command=close_app, font=("Arial", 10))
        button.pack(pady=10)
        
        print("✅ تم إنشاء واجهة المستخدم البسيطة بنجاح")
        
        # تشغيل لمدة قصيرة ثم إغلاق
        root.after(2000, close_app)  # إغلاق بعد ثانيتين
        root.mainloop()
        
        print("✅ تم إغلاق واجهة المستخدم بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("بدء اختبار النظام")
    print("=" * 50)
    
    # اختبار قاعدة البيانات
    print("\n1. اختبار قاعدة البيانات:")
    db_ok = test_database()
    
    # اختبار واجهة المستخدم
    print("\n2. اختبار واجهة المستخدم:")
    ui_ok = test_ui()
    
    # النتائج
    print("\n" + "=" * 50)
    print("نتائج الاختبار:")
    print("=" * 50)
    print(f"قاعدة البيانات: {'✅ تعمل' if db_ok else '❌ لا تعمل'}")
    print(f"واجهة المستخدم: {'✅ تعمل' if ui_ok else '❌ لا تعمل'}")
    
    if db_ok and ui_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى حل")

if __name__ == "__main__":
    main()
